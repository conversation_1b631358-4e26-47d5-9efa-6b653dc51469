<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - HITS Security</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>HITS Security</h1>
                </div>
                <p>نظام إدارة أمن المعلومات</p>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group" id="twoFactorGroup" style="display: none;">
                    <label for="twoFactorCode">رمز التحقق الثنائي</label>
                    <div class="input-group">
                        <i class="fas fa-key"></i>
                        <input type="text" id="twoFactorCode" name="twoFactorCode" maxlength="6" placeholder="000000">
                    </div>
                    <small class="help-text">أدخل الرمز المكون من 6 أرقام من تطبيق المصادقة</small>
                </div>

                <div class="form-options">
                    <label class="checkbox">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <div class="login-footer">
                <div class="security-info">
                    <i class="fas fa-shield-alt"></i>
                    <span>اتصال آمن ومشفر</span>
                </div>
                <div class="theme-toggle-container">
                    <button class="theme-toggle" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="login-bg">
            <div class="security-animation">
                <div class="shield-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="pulse-rings">
                    <div class="pulse-ring"></div>
                    <div class="pulse-ring"></div>
                    <div class="pulse-ring"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Modal -->
    <div class="modal" id="alertModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="alertTitle">تنبيه</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p id="alertMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal()">موافق</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if already logged in
            if (isAuthenticated()) {
                window.location.href = 'index.html';
            }

            // Initialize login form
            initializeLoginForm();
        });

        function initializeLoginForm() {
            const form = document.getElementById('loginForm');
            form.addEventListener('submit', handleLogin);
        }

        async function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const twoFactorCode = document.getElementById('twoFactorCode').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            const loginBtn = document.getElementById('loginBtn');
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحقق...';
            loginBtn.disabled = true;

            try {
                const result = await authenticateUser(username, password, twoFactorCode);
                
                if (result.requiresTwoFactor && !twoFactorCode) {
                    document.getElementById('twoFactorGroup').style.display = 'block';
                    showAlert('تنبيه', 'يرجى إدخال رمز التحقق الثنائي');
                } else if (result.success) {
                    // Log successful login
                    logSecurityEvent('LOGIN_SUCCESS', 'User login successful', 'INFO', {
                        username: username,
                        timestamp: new Date().toISOString(),
                        ip: 'localhost',
                        userAgent: navigator.userAgent
                    });

                    if (rememberMe) {
                        localStorage.setItem('rememberUser', 'true');
                    }

                    showAlert('نجح', 'تم تسجيل الدخول بنجاح', () => {
                        window.location.href = 'index.html';
                    });
                } else {
                    // Log failed login attempt
                    logSecurityEvent('LOGIN_FAILED', 'Failed login attempt', 'WARNING', {
                        username: username,
                        timestamp: new Date().toISOString(),
                        ip: 'localhost',
                        reason: result.error || 'Invalid credentials'
                    });

                    showAlert('خطأ', result.error || 'بيانات الدخول غير صحيحة');
                }
            } catch (error) {
                logSecurityEvent('LOGIN_ERROR', 'Login system error', 'ERROR', {
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                
                showAlert('خطأ', 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى.');
            } finally {
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول';
                loginBtn.disabled = false;
            }
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }

        function showAlert(title, message, callback) {
            document.getElementById('alertTitle').textContent = title;
            document.getElementById('alertMessage').textContent = message;
            document.getElementById('alertModal').style.display = 'flex';
            
            if (callback) {
                document.querySelector('#alertModal .btn-primary').onclick = function() {
                    closeModal();
                    callback();
                };
            }
        }

        function closeModal() {
            document.getElementById('alertModal').style.display = 'none';
        }
    </script>
</body>
</html>
