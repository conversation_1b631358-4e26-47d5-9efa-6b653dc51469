<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بدء تشغيل نظام HITS Security</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #fff;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature h3 {
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .feature p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .start-button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .demo-button {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }
        
        .credentials {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .credentials h3 {
            margin-bottom: 1rem;
            color: #ffeaa7;
        }
        
        .cred-item {
            display: flex;
            justify-content: space-between;
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
        }
        
        .footer {
            margin-top: 2rem;
            opacity: 0.7;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <h1>نظام HITS Security</h1>
        <p class="subtitle">نظام إدارة أمن المعلومات المتقدم</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🔐</div>
                <h3>أمان متقدم</h3>
                <p>مصادقة ثنائية وتشفير البيانات</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>إدخال يدوي</h3>
                <p>إدخال الأحداث الأمنية يدوياً من الواجهة</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📝</div>
                <h3>تسجيل شامل</h3>
                <p>تسجيل وأرشفة جميع الأحداث الأمنية</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">⚙️</div>
                <h3>إعدادات مرنة</h3>
                <p>تخصيص كامل للنظام والتنبيهات</p>
            </div>
        </div>
        
        <div class="credentials">
            <h3><i class="fas fa-key"></i> بيانات الدخول التجريبية</h3>
            <div class="cred-item">
                <span><strong>المدير:</strong> admin</span>
                <span><strong>كلمة المرور:</strong> admin123</span>
            </div>
            <div class="cred-item">
                <span><strong>محلل الأمان:</strong> security</span>
                <span><strong>كلمة المرور:</strong> security123</span>
            </div>
            <div class="cred-item">
                <span><strong>المشاهد:</strong> viewer</span>
                <span><strong>كلمة المرور:</strong> viewer123</span>
            </div>
        </div>
        
        <div>
            <a href="login.html" class="start-button">
                <i class="fas fa-sign-in-alt"></i>
                دخول النظام
            </a>
            
            <a href="index.html" class="start-button demo-button">
                <i class="fas fa-eye"></i>
                عرض تجريبي
            </a>
        </div>
        
        <div class="footer">
            <p>© 2024 HITS Security System - نظام إدارة أمن المعلومات</p>
            <p>تم التطوير باستخدام HTML5, CSS3, JavaScript</p>
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate features on load
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    feature.style.transition = 'all 0.6s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateY(0)';
                }, index * 200);
            });
            
            // Add hover effects to buttons
            const buttons = document.querySelectorAll('.start-button');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // Add click effect to credentials
            const credItems = document.querySelectorAll('.cred-item');
            credItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Copy to clipboard functionality could be added here
                    this.style.background = 'rgba(255, 255, 255, 0.2)';
                    setTimeout(() => {
                        this.style.background = 'rgba(0, 0, 0, 0.2)';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
