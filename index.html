<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة أمن المعلومات - HITS Security</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>HITS Security</span>
                </div>
                <nav class="nav">
                    <a href="index.html" class="nav-link active">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                    <a href="event-logs.html" class="nav-link">
                        <i class="fas fa-list-alt"></i>
                        سجل الأحداث
                    </a>
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                </nav>
                <div class="header-actions">
                    <button class="theme-toggle" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="user-btn">
                            <i class="fas fa-user"></i>
                            المدير
                        </button>
                        <div class="dropdown">
                            <a href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Welcome Section -->
            <section class="welcome-section">
                <div class="welcome-card">
                    <h1>مرحباً بك في نظام إدارة أمن المعلومات</h1>
                    <p>نظام شامل لمراقبة وإدارة الأمان السيبراني وتسجيل الأحداث الأمنية</p>
                    <div class="status-indicator">
                        <span class="status-dot active"></span>
                        النظام يعمل بشكل طبيعي
                    </div>
                </div>
            </section>

            <!-- Quick Stats -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="critical-alerts">0</h3>
                            <p>تنبيهات حرجة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="security-events">0</h3>
                            <p>أحداث أمنية اليوم</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="active-users">0</h3>
                            <p>المستخدمون النشطون</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="system-health">98%</h3>
                            <p>صحة النظام</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Manual Event Entry -->
            <section class="manual-entry-section">
                <div class="section-header">
                    <h2>
                        <i class="fas fa-plus-circle"></i>
                        إدخال حدث أمني يدوياً
                    </h2>
                    <button class="btn btn-primary" onclick="toggleEventForm()">
                        <i class="fas fa-plus"></i>
                        إضافة حدث جديد
                    </button>
                </div>

                <div class="event-form-container" id="eventFormContainer" style="display: none;">
                    <div class="event-form-card">
                        <div class="event-templates">
                            <span style="font-weight: 600; color: var(--text-secondary); margin-left: 10px;">قوالب سريعة:</span>
                            <button type="button" class="template-btn" onclick="useEventTemplate('login_failed')">
                                <i class="fas fa-user-times"></i> فشل تسجيل دخول
                            </button>
                            <button type="button" class="template-btn" onclick="useEventTemplate('threat_detected')">
                                <i class="fas fa-exclamation-triangle"></i> تهديد مكتشف
                            </button>
                            <button type="button" class="template-btn" onclick="useEventTemplate('system_update')">
                                <i class="fas fa-sync"></i> تحديث النظام
                            </button>
                            <button type="button" class="template-btn" onclick="fillCurrentIP()">
                                <i class="fas fa-map-marker-alt"></i> IP الحالي
                            </button>
                        </div>

                        <form id="manualEventForm" onsubmit="submitManualEvent(event)">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="eventType">نوع الحدث</label>
                                    <select id="eventType" required>
                                        <option value="">اختر نوع الحدث</option>
                                        <optgroup label="المصادقة">
                                            <option value="LOGIN_SUCCESS">تسجيل دخول ناجح</option>
                                            <option value="LOGIN_FAILED">فشل تسجيل الدخول</option>
                                            <option value="LOGOUT">تسجيل خروج</option>
                                            <option value="PASSWORD_CHANGE">تغيير كلمة المرور</option>
                                        </optgroup>
                                        <optgroup label="الأمان">
                                            <option value="THREAT_DETECTED">تهديد مكتشف</option>
                                            <option value="MALWARE_DETECTED">برمجية خبيثة</option>
                                            <option value="SUSPICIOUS_ACTIVITY">نشاط مشبوه</option>
                                            <option value="UNAUTHORIZED_ACCESS">وصول غير مصرح</option>
                                            <option value="DATA_BREACH_ATTEMPT">محاولة اختراق البيانات</option>
                                        </optgroup>
                                        <optgroup label="النظام">
                                            <option value="SYSTEM_UPDATE">تحديث النظام</option>
                                            <option value="SYSTEM_ERROR">خطأ في النظام</option>
                                            <option value="CONFIGURATION_CHANGE">تغيير الإعدادات</option>
                                            <option value="DATA_BACKUP">نسخ احتياطي</option>
                                        </optgroup>
                                        <optgroup label="الشبكة">
                                            <option value="FIREWALL_BLOCK">حظر جدار الحماية</option>
                                            <option value="NETWORK_ERROR">خطأ في الشبكة</option>
                                            <option value="PORT_SCAN_DETECTED">مسح المنافذ</option>
                                        </optgroup>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="eventSeverity">مستوى الخطورة</label>
                                    <select id="eventSeverity" required>
                                        <option value="">اختر مستوى الخطورة</option>
                                        <option value="INFO">معلومات</option>
                                        <option value="WARNING">تحذير</option>
                                        <option value="ERROR">خطأ</option>
                                        <option value="CRITICAL">حرج</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="eventSource">المصدر</label>
                                    <input type="text" id="eventSource" placeholder="مثال: web_application, firewall, system" required>
                                </div>

                                <div class="form-group">
                                    <label for="eventIP">عنوان IP</label>
                                    <input type="text" id="eventIP" placeholder="*************" pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="eventDescription">وصف الحدث</label>
                                <textarea id="eventDescription" rows="3" placeholder="اكتب وصفاً مفصلاً للحدث الأمني..." required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="eventMetadata">بيانات إضافية (اختياري)</label>
                                <textarea id="eventMetadata" rows="2" placeholder='{"key": "value", "additional_info": "data"}'></textarea>
                                <small class="help-text">أدخل البيانات الإضافية بصيغة JSON</small>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" onclick="cancelEventForm()">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ الحدث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Recent Events -->
            <section class="events-section">
                <div class="section-header">
                    <h2>الأحداث الأخيرة</h2>
                    <a href="event-logs.html" class="view-all-btn">
                        عرض الكل
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="events-table-container">
                    <table class="events-table">
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>نوع الحدث</th>
                                <th>المصدر</th>
                                <th>الوصف</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="recent-events">
                            <!-- Events will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Manual Event Entry -->
            <section class="manual-entry-section">
                <div class="section-header">
                    <h2>
                        <i class="fas fa-plus-circle"></i>
                        إدخال حدث أمني يدوياً
                    </h2>
                    <button class="btn btn-primary" onclick="toggleEventForm()">
                        <i class="fas fa-plus"></i>
                        إضافة حدث جديد
                    </button>
                </div>

                <div class="event-form-container" id="eventFormContainer" style="display: none;">
                    <div class="event-form-card">
                        <form id="manualEventForm" onsubmit="submitManualEvent(event)">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="eventType">نوع الحدث</label>
                                    <select id="eventType" required>
                                        <option value="">اختر نوع الحدث</option>
                                        <optgroup label="المصادقة">
                                            <option value="LOGIN_SUCCESS">تسجيل دخول ناجح</option>
                                            <option value="LOGIN_FAILED">فشل تسجيل الدخول</option>
                                            <option value="LOGOUT">تسجيل خروج</option>
                                            <option value="PASSWORD_CHANGE">تغيير كلمة المرور</option>
                                        </optgroup>
                                        <optgroup label="الأمان">
                                            <option value="THREAT_DETECTED">تهديد مكتشف</option>
                                            <option value="MALWARE_DETECTED">برمجية خبيثة</option>
                                            <option value="SUSPICIOUS_ACTIVITY">نشاط مشبوه</option>
                                            <option value="UNAUTHORIZED_ACCESS">وصول غير مصرح</option>
                                            <option value="DATA_BREACH_ATTEMPT">محاولة اختراق البيانات</option>
                                        </optgroup>
                                        <optgroup label="النظام">
                                            <option value="SYSTEM_UPDATE">تحديث النظام</option>
                                            <option value="SYSTEM_ERROR">خطأ في النظام</option>
                                            <option value="CONFIGURATION_CHANGE">تغيير الإعدادات</option>
                                            <option value="DATA_BACKUP">نسخ احتياطي</option>
                                        </optgroup>
                                        <optgroup label="الشبكة">
                                            <option value="FIREWALL_BLOCK">حظر جدار الحماية</option>
                                            <option value="NETWORK_ERROR">خطأ في الشبكة</option>
                                            <option value="PORT_SCAN_DETECTED">مسح المنافذ</option>
                                        </optgroup>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="eventSeverity">مستوى الخطورة</label>
                                    <select id="eventSeverity" required>
                                        <option value="">اختر مستوى الخطورة</option>
                                        <option value="INFO">معلومات</option>
                                        <option value="WARNING">تحذير</option>
                                        <option value="ERROR">خطأ</option>
                                        <option value="CRITICAL">حرج</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="eventSource">المصدر</label>
                                    <input type="text" id="eventSource" placeholder="مثال: web_application, firewall, system" required>
                                </div>

                                <div class="form-group">
                                    <label for="eventIP">عنوان IP</label>
                                    <input type="text" id="eventIP" placeholder="*************" pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="eventDescription">وصف الحدث</label>
                                <textarea id="eventDescription" rows="3" placeholder="اكتب وصفاً مفصلاً للحدث الأمني..." required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="eventMetadata">بيانات إضافية (اختياري)</label>
                                <textarea id="eventMetadata" rows="2" placeholder='{"key": "value", "additional_info": "data"}'></textarea>
                                <small class="help-text">أدخل البيانات الإضافية بصيغة JSON</small>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" onclick="cancelEventForm()">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ الحدث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="actions-section">
                <div class="section-header">
                    <h2>إجراءات سريعة</h2>
                </div>
                <div class="actions-grid">
                    <button class="action-card" onclick="generateReport()">
                        <i class="fas fa-file-alt"></i>
                        <span>إنشاء تقرير</span>
                    </button>
                    <button class="action-card" onclick="runSecurityScan()">
                        <i class="fas fa-search"></i>
                        <span>فحص أمني</span>
                    </button>
                    <button class="action-card" onclick="backupData()">
                        <i class="fas fa-download"></i>
                        <span>نسخ احتياطي</span>
                    </button>
                    <button class="action-card" onclick="updateRules()">
                        <i class="fas fa-sync"></i>
                        <span>تحديث القواعد</span>
                    </button>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 HITS Security System. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
    <script src="auth.js"></script>
    <script src="event-logger.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadDashboardData();
            loadRecentEvents();
            startRealTimeUpdates();
        });

        // Manual Event Entry Functions
        function toggleEventForm() {
            const container = document.getElementById('eventFormContainer');
            const isVisible = container.style.display !== 'none';

            if (isVisible) {
                container.style.display = 'none';
            } else {
                container.style.display = 'block';
                // Reset form
                document.getElementById('manualEventForm').reset();
                // Focus on first field
                document.getElementById('eventType').focus();
            }
        }

        function cancelEventForm() {
            document.getElementById('eventFormContainer').style.display = 'none';
            document.getElementById('manualEventForm').reset();
        }

        function submitManualEvent(event) {
            event.preventDefault();

            // Get form data
            const eventType = document.getElementById('eventType').value;
            const eventSeverity = document.getElementById('eventSeverity').value;
            const eventSource = document.getElementById('eventSource').value;
            const eventIP = document.getElementById('eventIP').value;
            const eventDescription = document.getElementById('eventDescription').value;
            const eventMetadata = document.getElementById('eventMetadata').value;

            // Validate required fields
            if (!eventType || !eventSeverity || !eventSource || !eventDescription) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // Parse metadata if provided
            let metadata = {};
            if (eventMetadata.trim()) {
                try {
                    metadata = JSON.parse(eventMetadata);
                } catch (error) {
                    showNotification('تنسيق البيانات الإضافية غير صحيح. يجب أن يكون بصيغة JSON صالحة', 'error');
                    return;
                }
            }

            // Add additional metadata
            metadata.manualEntry = true;
            metadata.entryTime = new Date().toISOString();
            metadata.enteredBy = getCurrentUser()?.username || 'unknown';

            if (eventIP) {
                metadata.ip = eventIP;
            }

            // Log the event
            try {
                const eventId = logSecurityEvent(eventType, eventDescription, eventSeverity, {
                    source: eventSource,
                    ...metadata
                });

                showNotification('تم حفظ الحدث بنجاح', 'success');

                // Reset form and hide it
                cancelEventForm();

                // Refresh recent events
                loadRecentEvents();

                // Update dashboard stats
                loadDashboardData();

                // Log the manual entry action
                logSecurityEvent('MANUAL_EVENT_ENTRY', `Manual event entry: ${eventType}`, 'INFO', {
                    originalEventId: eventId,
                    eventType: eventType,
                    severity: eventSeverity,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                console.error('Error saving event:', error);
                showNotification('حدث خطأ أثناء حفظ الحدث', 'error');
            }
        }

        // Auto-fill IP address with current user's IP (simulated)
        function fillCurrentIP() {
            document.getElementById('eventIP').value = '*************';
        }

        // Preset event templates
        function useEventTemplate(template) {
            const templates = {
                'login_failed': {
                    type: 'LOGIN_FAILED',
                    severity: 'WARNING',
                    source: 'web_application',
                    description: 'محاولة تسجيل دخول فاشلة'
                },
                'threat_detected': {
                    type: 'THREAT_DETECTED',
                    severity: 'CRITICAL',
                    source: 'security_scanner',
                    description: 'تم اكتشاف تهديد أمني محتمل'
                },
                'system_update': {
                    type: 'SYSTEM_UPDATE',
                    severity: 'INFO',
                    source: 'system',
                    description: 'تم تحديث النظام بنجاح'
                }
            };

            const selectedTemplate = templates[template];
            if (selectedTemplate) {
                document.getElementById('eventType').value = selectedTemplate.type;
                document.getElementById('eventSeverity').value = selectedTemplate.severity;
                document.getElementById('eventSource').value = selectedTemplate.source;
                document.getElementById('eventDescription').value = selectedTemplate.description;
            }
        }

        // Add event listener for IP validation
        document.addEventListener('DOMContentLoaded', function() {
            const ipInput = document.getElementById('eventIP');
            if (ipInput) {
                ipInput.addEventListener('blur', function() {
                    const ip = this.value;
                    if (ip && !isValidIP(ip)) {
                        showNotification('تنسيق عنوان IP غير صحيح', 'warning');
                        this.focus();
                    }
                });
            }
        });

        function isValidIP(ip) {
            const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            return ipRegex.test(ip);
        }
    </script>
</body>
</html>
