// Settings Management Module

// Default Settings Configuration
const DEFAULT_SETTINGS = {
    general: {
        language: 'ar',
        timezone: 'Asia/Riyadh',
        darkMode: false,
        autoRefresh: true,
        pageSize: 25
    },
    security: {
        twoFactor: true,
        sessionTimeout: 30,
        maxLoginAttempts: 3,
        lockoutDuration: 15,
        encryption: true,
        autoScan: true,
        scanInterval: 24
    },
    notifications: {
        criticalAlerts: true,
        emailAlerts: false,
        smsAlerts: false,
        desktopAlerts: true,
        alertFrequency: 15
    },
    logging: {
        logLevel: 'INFO',
        logRetention: 30,
        logCompression: true,
        autoExport: false,
        exportInterval: 'weekly',
        siemIntegration: false
    },
    users: {
        allowRegistration: false,
        requireEmailVerification: true,
        passwordComplexity: 'high',
        passwordExpiry: 90
    },
    backup: {
        autoBackup: true,
        backupInterval: 'daily',
        backupRetention: 30,
        backupLocation: 'local'
    },
    system: {
        maintenanceMode: false,
        debugMode: false,
        performanceMonitoring: true,
        updateNotifications: true
    }
};

let currentSettings = {};

// Initialize Settings
function initializeSettings() {
    loadSettings();
    setupEventListeners();
    updateUIFromSettings();
}

function loadSettings() {
    try {
        const savedSettings = localStorage.getItem('hits_security_settings');
        if (savedSettings) {
            currentSettings = { ...DEFAULT_SETTINGS, ...JSON.parse(savedSettings) };
        } else {
            currentSettings = { ...DEFAULT_SETTINGS };
        }
    } catch (error) {
        console.error('Error loading settings:', error);
        currentSettings = { ...DEFAULT_SETTINGS };
    }
}

function saveSettings() {
    try {
        localStorage.setItem('hits_security_settings', JSON.stringify(currentSettings));
        showNotification('تم حفظ الإعدادات بنجاح', 'success');
        
        logSecurityEvent('SETTINGS_SAVED', 'System settings updated', 'INFO', {
            timestamp: new Date().toISOString(),
            user: getCurrentUser()?.username || 'system'
        });
        
        applySettings();
    } catch (error) {
        console.error('Error saving settings:', error);
        showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
    }
}

function applySettings() {
    // Apply theme
    if (currentSettings.general.darkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
    } else {
        document.documentElement.setAttribute('data-theme', 'light');
    }
    
    // Apply language
    document.documentElement.lang = currentSettings.general.language;
    
    // Apply other settings that need immediate effect
    updateThemeIcon();
}

// Settings Navigation
function showSettingsSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.settings-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Show selected section
    const targetSection = document.getElementById(sectionId + '-settings');
    if (targetSection) {
        targetSection.style.display = 'block';
    }
    
    // Update navigation
    document.querySelectorAll('.settings-nav a').forEach(link => {
        link.classList.remove('active');
    });
    
    const activeLink = document.querySelector(`.settings-nav a[href="#${sectionId}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
    
    logSecurityEvent('SETTINGS_NAVIGATION', `Navigated to ${sectionId} settings`, 'INFO', {
        section: sectionId,
        timestamp: new Date().toISOString()
    });
}

// Toggle Functions
function toggleDarkMode() {
    const toggle = document.getElementById('darkModeToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.general.darkMode = false;
    } else {
        toggle.classList.add('active');
        currentSettings.general.darkMode = true;
    }
    
    applySettings();
}

function toggleAutoRefresh() {
    const toggle = document.getElementById('autoRefreshToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.general.autoRefresh = false;
    } else {
        toggle.classList.add('active');
        currentSettings.general.autoRefresh = true;
    }
}

function toggleTwoFactor() {
    const toggle = document.getElementById('twoFactorToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        if (confirm('هل أنت متأكد من إلغاء تفعيل المصادقة الثنائية؟ هذا قد يقلل من أمان النظام.')) {
            toggle.classList.remove('active');
            currentSettings.security.twoFactor = false;
            showNotification('تم إلغاء تفعيل المصادقة الثنائية', 'warning');
        }
    } else {
        toggle.classList.add('active');
        currentSettings.security.twoFactor = true;
        showNotification('تم تفعيل المصادقة الثنائية', 'success');
    }
}

function toggleEncryption() {
    const toggle = document.getElementById('encryptionToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        if (confirm('هل أنت متأكد من إلغاء تفعيل التشفير؟ هذا قد يعرض البيانات للخطر.')) {
            toggle.classList.remove('active');
            currentSettings.security.encryption = false;
            showNotification('تم إلغاء تفعيل التشفير', 'warning');
        }
    } else {
        toggle.classList.add('active');
        currentSettings.security.encryption = true;
        showNotification('تم تفعيل التشفير', 'success');
    }
}

function toggleAutoScan() {
    const toggle = document.getElementById('autoScanToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.security.autoScan = false;
    } else {
        toggle.classList.add('active');
        currentSettings.security.autoScan = true;
    }
}

function toggleCriticalAlerts() {
    const toggle = document.getElementById('criticalAlertsToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.notifications.criticalAlerts = false;
    } else {
        toggle.classList.add('active');
        currentSettings.notifications.criticalAlerts = true;
    }
}

function toggleEmailAlerts() {
    const toggle = document.getElementById('emailAlertsToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.notifications.emailAlerts = false;
    } else {
        toggle.classList.add('active');
        currentSettings.notifications.emailAlerts = true;
    }
}

function toggleSmsAlerts() {
    const toggle = document.getElementById('smsAlertsToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.notifications.smsAlerts = false;
    } else {
        toggle.classList.add('active');
        currentSettings.notifications.smsAlerts = true;
    }
}

function toggleDesktopAlerts() {
    const toggle = document.getElementById('desktopAlertsToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.notifications.desktopAlerts = false;
    } else {
        toggle.classList.add('active');
        currentSettings.notifications.desktopAlerts = true;
        
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    showNotification('تم تفعيل تنبيهات سطح المكتب', 'success');
                } else {
                    showNotification('تم رفض إذن التنبيهات', 'warning');
                    toggle.classList.remove('active');
                    currentSettings.notifications.desktopAlerts = false;
                }
            });
        }
    }
}

function toggleLogCompression() {
    const toggle = document.getElementById('logCompressionToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.logging.logCompression = false;
    } else {
        toggle.classList.add('active');
        currentSettings.logging.logCompression = true;
    }
}

function toggleAutoExport() {
    const toggle = document.getElementById('autoExportToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.logging.autoExport = false;
    } else {
        toggle.classList.add('active');
        currentSettings.logging.autoExport = true;
    }
}

function toggleSiemIntegration() {
    const toggle = document.getElementById('siemIntegrationToggle');
    const isActive = toggle.classList.contains('active');
    
    if (isActive) {
        toggle.classList.remove('active');
        currentSettings.logging.siemIntegration = false;
    } else {
        toggle.classList.add('active');
        currentSettings.logging.siemIntegration = true;
        showNotification('تم تفعيل تكامل SIEM', 'info');
    }
}

// Settings Actions
function saveAllSettings() {
    // Collect all form values
    collectFormValues();
    saveSettings();
}

function resetToDefaults() {
    if (confirm('هل أنت متأكد من استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع التخصيصات الحالية.')) {
        currentSettings = { ...DEFAULT_SETTINGS };
        updateUIFromSettings();
        saveSettings();
        showNotification('تم استعادة الإعدادات الافتراضية', 'info');
        
        logSecurityEvent('SETTINGS_RESET', 'Settings reset to defaults', 'WARNING', {
            timestamp: new Date().toISOString(),
            user: getCurrentUser()?.username || 'system'
        });
    }
}

function collectFormValues() {
    // General settings
    currentSettings.general.language = document.getElementById('languageSetting')?.value || 'ar';
    currentSettings.general.timezone = document.getElementById('timezoneSetting')?.value || 'Asia/Riyadh';
    currentSettings.general.pageSize = parseInt(document.getElementById('pageSize')?.value) || 25;
    
    // Security settings
    currentSettings.security.sessionTimeout = parseInt(document.getElementById('sessionTimeout')?.value) || 30;
    currentSettings.security.maxLoginAttempts = parseInt(document.getElementById('maxLoginAttempts')?.value) || 3;
    currentSettings.security.lockoutDuration = parseInt(document.getElementById('lockoutDuration')?.value) || 15;
    currentSettings.security.scanInterval = parseInt(document.getElementById('scanInterval')?.value) || 24;
    
    // Notification settings
    currentSettings.notifications.alertFrequency = parseInt(document.getElementById('alertFrequency')?.value) || 15;
    
    // Logging settings
    currentSettings.logging.logLevel = document.getElementById('logLevel')?.value || 'INFO';
    currentSettings.logging.logRetention = parseInt(document.getElementById('logRetention')?.value) || 30;
    currentSettings.logging.exportInterval = document.getElementById('exportInterval')?.value || 'weekly';
}

function updateUIFromSettings() {
    // Update general settings
    if (document.getElementById('languageSetting')) {
        document.getElementById('languageSetting').value = currentSettings.general.language;
    }
    if (document.getElementById('timezoneSetting')) {
        document.getElementById('timezoneSetting').value = currentSettings.general.timezone;
    }
    if (document.getElementById('pageSize')) {
        document.getElementById('pageSize').value = currentSettings.general.pageSize;
    }
    
    // Update toggles
    updateToggle('darkModeToggle', currentSettings.general.darkMode);
    updateToggle('autoRefreshToggle', currentSettings.general.autoRefresh);
    updateToggle('twoFactorToggle', currentSettings.security.twoFactor);
    updateToggle('encryptionToggle', currentSettings.security.encryption);
    updateToggle('autoScanToggle', currentSettings.security.autoScan);
    updateToggle('criticalAlertsToggle', currentSettings.notifications.criticalAlerts);
    updateToggle('emailAlertsToggle', currentSettings.notifications.emailAlerts);
    updateToggle('smsAlertsToggle', currentSettings.notifications.smsAlerts);
    updateToggle('desktopAlertsToggle', currentSettings.notifications.desktopAlerts);
    updateToggle('logCompressionToggle', currentSettings.logging.logCompression);
    updateToggle('autoExportToggle', currentSettings.logging.autoExport);
    updateToggle('siemIntegrationToggle', currentSettings.logging.siemIntegration);
    
    // Update security settings
    if (document.getElementById('sessionTimeout')) {
        document.getElementById('sessionTimeout').value = currentSettings.security.sessionTimeout;
    }
    if (document.getElementById('maxLoginAttempts')) {
        document.getElementById('maxLoginAttempts').value = currentSettings.security.maxLoginAttempts;
    }
    if (document.getElementById('lockoutDuration')) {
        document.getElementById('lockoutDuration').value = currentSettings.security.lockoutDuration;
    }
    if (document.getElementById('scanInterval')) {
        document.getElementById('scanInterval').value = currentSettings.security.scanInterval;
    }
    
    // Update notification settings
    if (document.getElementById('alertFrequency')) {
        document.getElementById('alertFrequency').value = currentSettings.notifications.alertFrequency;
    }
    
    // Update logging settings
    if (document.getElementById('logLevel')) {
        document.getElementById('logLevel').value = currentSettings.logging.logLevel;
    }
    if (document.getElementById('logRetention')) {
        document.getElementById('logRetention').value = currentSettings.logging.logRetention;
    }
    if (document.getElementById('exportInterval')) {
        document.getElementById('exportInterval').value = currentSettings.logging.exportInterval;
    }
}

function updateToggle(toggleId, isActive) {
    const toggle = document.getElementById(toggleId);
    if (toggle) {
        if (isActive) {
            toggle.classList.add('active');
        } else {
            toggle.classList.remove('active');
        }
    }
}

function setupEventListeners() {
    // Add change listeners to form elements
    const formElements = document.querySelectorAll('select, input');
    formElements.forEach(element => {
        element.addEventListener('change', function() {
            // Auto-save on change (optional)
            // collectFormValues();
        });
    });
}

// Settings Validation
function validateSettings() {
    const errors = [];
    
    // Validate session timeout
    if (currentSettings.security.sessionTimeout < 5 || currentSettings.security.sessionTimeout > 480) {
        errors.push('مدة انتهاء الجلسة يجب أن تكون بين 5 و 480 دقيقة');
    }
    
    // Validate max login attempts
    if (currentSettings.security.maxLoginAttempts < 1 || currentSettings.security.maxLoginAttempts > 20) {
        errors.push('عدد محاولات تسجيل الدخول يجب أن يكون بين 1 و 20');
    }
    
    // Validate lockout duration
    if (currentSettings.security.lockoutDuration < 1 || currentSettings.security.lockoutDuration > 1440) {
        errors.push('مدة قفل الحساب يجب أن تكون بين 1 و 1440 دقيقة');
    }
    
    // Validate log retention
    if (currentSettings.logging.logRetention < 0 || currentSettings.logging.logRetention > 3650) {
        errors.push('مدة الاحتفاظ بالسجلات يجب أن تكون بين 0 و 3650 يوم');
    }
    
    return errors;
}

// Export/Import Settings
function exportSettings() {
    const settingsData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        settings: currentSettings
    };
    
    const dataStr = JSON.stringify(settingsData, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `hits-security-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showNotification('تم تصدير الإعدادات بنجاح', 'success');
    
    logSecurityEvent('SETTINGS_EXPORTED', 'System settings exported', 'INFO', {
        timestamp: new Date().toISOString()
    });
}

function importSettings(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedData = JSON.parse(e.target.result);
            
            if (importedData.settings) {
                currentSettings = { ...DEFAULT_SETTINGS, ...importedData.settings };
                updateUIFromSettings();
                saveSettings();
                showNotification('تم استيراد الإعدادات بنجاح', 'success');
                
                logSecurityEvent('SETTINGS_IMPORTED', 'System settings imported', 'INFO', {
                    timestamp: new Date().toISOString(),
                    version: importedData.version
                });
            } else {
                throw new Error('Invalid settings file format');
            }
        } catch (error) {
            console.error('Error importing settings:', error);
            showNotification('خطأ في استيراد الإعدادات', 'error');
        }
    };
    reader.readAsText(file);
}

// Get current settings
function getCurrentSettings() {
    return currentSettings;
}

function getSetting(category, key) {
    return currentSettings[category] && currentSettings[category][key];
}

function setSetting(category, key, value) {
    if (!currentSettings[category]) {
        currentSettings[category] = {};
    }
    currentSettings[category][key] = value;
}

// Export functions for global use
window.HITSSettings = {
    initializeSettings,
    loadSettings,
    saveSettings,
    getCurrentSettings,
    getSetting,
    setSetting,
    exportSettings,
    importSettings,
    showSettingsSection,
    saveAllSettings,
    resetToDefaults
};
