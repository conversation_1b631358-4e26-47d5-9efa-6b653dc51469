/* CSS Variables for Theme Management */
:root {
    /* Light Theme Colors */
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --accent-color: #0ea5e9;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --bg-card: #ffffff;
    --bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;
    
    /* Border Colors */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    --border-focus: #2563eb;
    
    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-overlay: rgba(0, 0, 0, 0.7);
    
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    
    --border-color: #334155;
    --border-hover: #475569;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Header */
.header {
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.logo i {
    font-size: 2rem;
}

.nav {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--bg-tertiary);
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.theme-toggle:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.user-menu {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.user-btn:hover {
    background-color: var(--bg-tertiary);
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.user-menu:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown a {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--text-primary);
    transition: background-color var(--transition-fast);
}

.dropdown a:hover {
    background-color: var(--bg-tertiary);
}

/* Main Content */
.main {
    padding: var(--spacing-xl) 0;
    min-height: calc(100vh - 200px);
}

/* Welcome Section */
.welcome-section {
    margin-bottom: var(--spacing-2xl);
}

.welcome-card {
    background-color: var(--bg-card);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
}

.welcome-card h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.welcome-card p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    color: var(--success-color);
    font-weight: 600;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Stats Section */
.stats-section {
    margin-bottom: var(--spacing-2xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    background-color: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: transform var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-inverse);
}

.stat-card:nth-child(1) .stat-icon {
    background-color: var(--error-color);
}

.stat-card:nth-child(2) .stat-icon {
    background-color: var(--primary-color);
}

.stat-card:nth-child(3) .stat-icon {
    background-color: var(--success-color);
}

.stat-card:nth-child(4) .stat-icon {
    background-color: var(--info-color);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
}

.view-all-btn:hover {
    color: var(--accent-color);
}

/* Events Section */
.events-section {
    margin-bottom: var(--spacing-2xl);
}

.events-table-container {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.events-table {
    width: 100%;
    border-collapse: collapse;
}

.events-table th,
.events-table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.events-table th {
    background-color: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
}

.events-table td {
    color: var(--text-secondary);
}

.events-table tr:hover {
    background-color: var(--bg-secondary);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-badge.error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.status-badge.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

/* Actions Section */
.actions-section {
    margin-bottom: var(--spacing-2xl);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.action-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    color: var(--text-primary);
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.action-card i {
    font-size: 2rem;
    color: var(--primary-color);
}

.action-card span {
    font-weight: 500;
}

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1000px;
    width: 100%;
    margin: var(--spacing-lg);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.login-card {
    background-color: var(--bg-card);
    padding: var(--spacing-2xl);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.login-header .logo {
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.login-header h1 {
    font-size: 2rem;
    color: var(--primary-color);
}

.login-header p {
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: var(--spacing-md);
    color: var(--text-muted);
    z-index: 1;
}

.input-group input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-2xl) var(--spacing-md) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all var(--transition-fast);
}

.input-group input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.toggle-password {
    position: absolute;
    left: var(--spacing-md);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    z-index: 1;
}

.form-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.checkbox input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox input:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-btn {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.login-btn:hover:not(:disabled) {
    background-color: var(--accent-color);
    transform: translateY(-1px);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.login-footer {
    margin-top: var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.security-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--success-color);
    font-size: 0.9rem;
}

.login-bg {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(14, 165, 233, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.security-animation {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.shield-icon {
    font-size: 6rem;
    color: rgba(255, 255, 255, 0.9);
    z-index: 2;
    position: relative;
}

.pulse-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.pulse-ring {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: pulse-ring 2s infinite;
}

.pulse-ring:nth-child(1) {
    width: 100px;
    height: 100px;
    margin: -50px 0 0 -50px;
    animation-delay: 0s;
}

.pulse-ring:nth-child(2) {
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    animation-delay: 0.5s;
}

.pulse-ring:nth-child(3) {
    width: 200px;
    height: 200px;
    margin: -100px 0 0 -100px;
    animation-delay: 1s;
}

@keyframes pulse-ring {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-overlay);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--text-primary);
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.close-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-secondary {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: var(--bg-tertiary);
}

/* Footer */
.footer {
    background-color: var(--bg-card);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg) 0;
    text-align: center;
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .login-container {
        grid-template-columns: 1fr;
        margin: var(--spacing-md);
    }
    
    .login-bg {
        display: none;
    }
    
    .welcome-card h1 {
        font-size: 2rem;
    }
    
    .events-table-container {
        overflow-x: auto;
    }
    
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.hidden { display: none; }
.visible { display: block; }

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* Event Logs Page Styles */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl);
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.page-title h1 {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.page-title p {
    color: var(--text-secondary);
    margin: 0;
}

.page-actions {
    display: flex;
    gap: var(--spacing-md);
}

.filters-section {
    margin-bottom: var(--spacing-xl);
}

.filters-card {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.filters-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.filters-header h3 {
    margin: 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filters-content {
    padding: var(--spacing-lg);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
}

.filter-group select,
.filter-group input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.table-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.separator {
    color: var(--border-color);
}

.table-controls select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.8rem;
}

.events-table th {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.events-table th:hover {
    background-color: var(--bg-secondary);
}

.events-table th i {
    margin-left: var(--spacing-xs);
    opacity: 0.5;
    transition: opacity var(--transition-fast);
}

.events-table th:hover i {
    opacity: 1;
}

.event-row {
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.event-row:hover {
    background-color: var(--bg-tertiary);
}

.description-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-footer {
    padding: var(--spacing-lg);
    background-color: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
}

.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.page-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.9rem;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-btn:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.page-btn.active {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.page-ellipsis {
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--text-muted);
}

.modal-large {
    max-width: 800px;
}

.event-detail-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.detail-item span {
    color: var(--text-primary);
}

.metadata-display {
    background-color: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    overflow-x: auto;
    white-space: pre-wrap;
    color: var(--text-primary);
}

/* Dashboard Chart Styles */
.chart-container {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.chart-header h3 {
    margin: 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.chart-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.chart-controls button {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.8rem;
}

.chart-controls button.active {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.chart-area {
    height: 300px;
    position: relative;
    overflow: hidden;
}

/* Settings Page Styles */
.settings-grid {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.settings-sidebar {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    height: fit-content;
}

.settings-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.settings-nav li {
    margin-bottom: var(--spacing-sm);
}

.settings-nav a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.settings-nav a:hover,
.settings-nav a.active {
    background-color: var(--bg-tertiary);
    color: var(--primary-color);
}

.settings-content {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
}

.settings-section {
    margin-bottom: var(--spacing-2xl);
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h3 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.settings-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--border-color);
}

.settings-row:last-child {
    border-bottom: none;
}

.setting-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.setting-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    width: 50px;
    height: 24px;
    background-color: var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.toggle-switch.active {
    background-color: var(--primary-color);
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    transition: transform var(--transition-normal);
}

.toggle-switch.active::after {
    transform: translateX(26px);
}

/* Alert Styles */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: var(--info-color);
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

.alert-error {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: var(--error-color);
}

/* Additional Dashboard Styles */
.threat-indicator {
    width: 100%;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-top: var(--spacing-sm);
}

.threat-bar {
    height: 100%;
    background-color: var(--success-color);
    transition: width var(--transition-normal), background-color var(--transition-normal);
    border-radius: 4px;
}

.alerts-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    max-height: 400px;
    overflow-y: auto;
}

.alert {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: var(--spacing-md);
}

.alert-content {
    flex: 1;
}

.alert-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.alert-title {
    font-weight: 600;
    color: var(--text-primary);
}

.alert-time {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-right: auto;
}

.alert-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.alert-source {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.alert-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.analysis-section {
    margin-bottom: var(--spacing-2xl);
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.analysis-card {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    margin: 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-content {
    padding: var(--spacing-lg);
    height: 300px;
    position: relative;
}

.threat-sources {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    max-height: 250px;
    overflow-y: auto;
}

.threat-source-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.source-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.source-ip {
    font-weight: 600;
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
}

.source-country {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.source-stats {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.threat-count {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.source-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
}

.source-status.blocked {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.source-status.allowed {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.source-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.health-section {
    margin-bottom: var(--spacing-2xl);
}

.health-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.health-item {
    background-color: var(--bg-card);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.health-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-lg);
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 1.2rem;
}

.health-info {
    flex: 1;
}

.health-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
}

.health-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    font-weight: 500;
}

.health-status.online {
    color: var(--success-color);
}

.health-status.offline {
    color: var(--error-color);
}

.health-status.warning {
    color: var(--warning-color);
}

.health-status .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
    animation: pulse 2s infinite;
}

.health-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Manual Event Entry Styles */
.manual-entry-section {
    margin-bottom: var(--spacing-2xl);
}

.event-form-container {
    margin-top: var(--spacing-lg);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.event-form-card {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-family: inherit;
    transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group select optgroup {
    font-weight: 600;
    color: var(--text-primary);
}

.form-group select option {
    padding: var(--spacing-sm);
    color: var(--text-primary);
}

.help-text {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.form-actions .btn {
    min-width: 120px;
}

/* Event Form Validation Styles */
.form-group input:invalid,
.form-group select:invalid,
.form-group textarea:invalid {
    border-color: var(--error-color);
}

.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
    border-color: var(--success-color);
}

/* Event Templates */
.event-templates {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.template-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.template-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive Form Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }

    .event-form-card {
        padding: var(--spacing-lg);
    }
}

/* Enhanced Button Styles for Form */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border: none;
    color: var(--text-inverse);
    font-weight: 600;
    transition: all var(--transition-fast);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-secondary {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 500;
}

.btn-secondary:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

/* Action Buttons in Table */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
    min-width: auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-error {
    background-color: var(--error-color);
    color: var(--text-inverse);
    border: 1px solid var(--error-color);
}

.btn-error:hover {
    background-color: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
}

/* Delete Confirmation Modal Styles */
.delete-warning {
    text-align: center;
    padding: var(--spacing-lg);
}

.delete-warning i {
    font-size: 3rem;
    color: var(--error-color);
    margin-bottom: var(--spacing-md);
}

.delete-warning p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.warning-text {
    color: var(--error-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.event-preview {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-top: var(--spacing-lg);
    text-align: right;
}

.event-preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-color);
}

.event-preview-item:last-child {
    border-bottom: none;
}

.event-preview-item strong {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Edit Form Specific Styles */
.modal-large .form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.modal-large .form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.modal-large .form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.modal-large .form-group input,
.modal-large .form-group select,
.modal-large .form-group textarea {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-family: inherit;
    transition: all var(--transition-fast);
}

.modal-large .form-group input:focus,
.modal-large .form-group select:focus,
.modal-large .form-group textarea:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.modal-large .form-group textarea {
    resize: vertical;
    min-height: 80px;
    font-family: 'Courier New', monospace;
}

.modal-large .help-text {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* Enhanced Button Hover Effects */
.btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

/* Table Row Hover Enhancement */
.event-row:hover .action-buttons {
    opacity: 1;
}

.action-buttons {
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

/* Responsive Action Buttons */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .btn-sm {
        width: 100%;
        height: auto;
        padding: var(--spacing-sm);
    }

    .modal-large .form-row {
        grid-template-columns: 1fr;
    }
}

/* Success/Error States for Form Fields */
.form-group input.success,
.form-group select.success,
.form-group textarea.success {
    border-color: var(--success-color);
    background-color: rgba(16, 185, 129, 0.05);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: var(--error-color);
    background-color: rgba(239, 68, 68, 0.05);
}

/* Loading State for Buttons */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .theme-toggle,
    .user-menu,
    .actions-section,
    .manual-entry-section,
    .action-buttons {
        display: none;
    }

    .main {
        padding: 0;
    }

    .events-table {
        font-size: 12px;
    }
}
