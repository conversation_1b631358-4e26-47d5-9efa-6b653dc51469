// Global Variables
let currentTheme = localStorage.getItem('theme') || 'light';
let dashboardData = {};
let realTimeInterval;

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    initializeApp();
});

// Theme Management
function initializeTheme() {
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateThemeIcon();
}

function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    localStorage.setItem('theme', currentTheme);
    updateThemeIcon();
    
    // Log theme change
    logSecurityEvent('THEME_CHANGED', `Theme changed to ${currentTheme}`, 'INFO', {
        theme: currentTheme,
        timestamp: new Date().toISOString()
    });
}

function updateThemeIcon() {
    const themeIcons = document.querySelectorAll('.theme-toggle i');
    themeIcons.forEach(icon => {
        icon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    });
}

// Application Initialization
function initializeApp() {
    // Check authentication on protected pages
    const protectedPages = ['index.html', 'security-dashboard.html', 'event-logs.html', 'settings.html'];
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    
    if (protectedPages.includes(currentPage) && !isAuthenticated()) {
        window.location.href = 'login.html';
        return;
    }
    
    // Initialize page-specific functionality
    switch (currentPage) {
        case 'index.html':
            initializeDashboard();
            break;
        case 'event-logs.html':
            initializeEventLogs();
            break;
        case 'security-dashboard.html':
            initializeSecurityDashboard();
            break;
        case 'settings.html':
            initializeSettings();
            break;
    }
}

// Dashboard Functions
function initializeDashboard() {
    loadDashboardData();
    loadRecentEvents();
    startRealTimeUpdates();
}

function loadDashboardData() {
    // Simulate loading dashboard statistics
    const stats = {
        criticalAlerts: Math.floor(Math.random() * 5),
        securityEvents: Math.floor(Math.random() * 50) + 10,
        activeUsers: Math.floor(Math.random() * 20) + 5,
        systemHealth: Math.floor(Math.random() * 10) + 90
    };
    
    updateDashboardStats(stats);
    dashboardData = stats;
}

function updateDashboardStats(stats) {
    const elements = {
        'critical-alerts': stats.criticalAlerts,
        'security-events': stats.securityEvents,
        'active-users': stats.activeUsers,
        'system-health': stats.systemHealth + '%'
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            animateCounter(element, value);
        }
    });
}

function animateCounter(element, targetValue) {
    const isPercentage = typeof targetValue === 'string' && targetValue.includes('%');
    const numericValue = isPercentage ? parseInt(targetValue) : targetValue;
    const duration = 1000;
    const startTime = Date.now();
    const startValue = 0;
    
    function updateCounter() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const currentValue = Math.floor(startValue + (numericValue - startValue) * progress);
        
        element.textContent = isPercentage ? currentValue + '%' : currentValue;
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    updateCounter();
}

function loadRecentEvents() {
    const recentEventsContainer = document.getElementById('recent-events');
    if (!recentEventsContainer) return;
    
    const events = getRecentSecurityEvents(5);
    
    recentEventsContainer.innerHTML = events.map(event => `
        <tr>
            <td>${formatDateTime(event.timestamp)}</td>
            <td>${getEventTypeDisplay(event.type)}</td>
            <td>${event.source || 'System'}</td>
            <td>${event.description}</td>
            <td><span class="status-badge ${event.severity.toLowerCase()}">${getSeverityDisplay(event.severity)}</span></td>
        </tr>
    `).join('');
}

function startRealTimeUpdates() {
    // Update dashboard every 30 seconds
    realTimeInterval = setInterval(() => {
        loadDashboardData();
        loadRecentEvents();
    }, 30000);
}

// Event Type and Severity Display Functions
function getEventTypeDisplay(type) {
    const types = {
        'LOGIN_SUCCESS': 'تسجيل دخول ناجح',
        'LOGIN_FAILED': 'فشل تسجيل الدخول',
        'LOGIN_ERROR': 'خطأ في تسجيل الدخول',
        'LOGOUT': 'تسجيل خروج',
        'PASSWORD_CHANGE': 'تغيير كلمة المرور',
        'SECURITY_SCAN': 'فحص أمني',
        'THREAT_DETECTED': 'تهديد مكتشف',
        'SYSTEM_UPDATE': 'تحديث النظام',
        'DATA_BACKUP': 'نسخ احتياطي',
        'ACCESS_DENIED': 'رفض الوصول',
        'SUSPICIOUS_ACTIVITY': 'نشاط مشبوه',
        'FIREWALL_BLOCK': 'حظر جدار الحماية',
        'MALWARE_DETECTED': 'برمجية خبيثة مكتشفة',
        'DATA_BREACH_ATTEMPT': 'محاولة اختراق البيانات',
        'UNAUTHORIZED_ACCESS': 'وصول غير مصرح',
        'SYSTEM_ERROR': 'خطأ في النظام',
        'CONFIGURATION_CHANGE': 'تغيير في الإعدادات',
        'USER_CREATED': 'إنشاء مستخدم جديد',
        'USER_DELETED': 'حذف مستخدم',
        'PERMISSION_CHANGE': 'تغيير الصلاحيات'
    };
    return types[type] || type;
}

function getSeverityDisplay(severity) {
    const severities = {
        'INFO': 'معلومات',
        'WARNING': 'تحذير',
        'ERROR': 'خطأ',
        'CRITICAL': 'حرج'
    };
    return severities[severity] || severity;
}



// Report Generation
function downloadReport(data) {
    const reportContent = `
تقرير أمان النظام
==================

تاريخ الإنشاء: ${formatDateTime(data.generatedAt)}

إحصائيات عامة:
- إجمالي الأحداث: ${data.totalEvents}
- التنبيهات الحرجة: ${data.criticalAlerts}
- الأحداث الأمنية اليوم: ${data.securityEvents}
- صحة النظام: ${data.systemHealth}%

الأحداث الأخيرة:
${getRecentSecurityEvents(10).map(event => 
    `- ${formatDateTime(event.timestamp)}: ${getEventTypeDisplay(event.type)} (${getSeverityDisplay(event.severity)})`
).join('\n')}

---
تم إنشاء هذا التقرير بواسطة نظام HITS Security
    `.trim();
    
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `security-report-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Notification System
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add notification styles if not already present
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--bg-card);
                border: 1px solid var(--border-color);
                border-radius: var(--radius-md);
                box-shadow: var(--shadow-lg);
                padding: var(--spacing-md);
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: var(--spacing-md);
                z-index: 1000;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
            }
            
            .notification-info { border-left: 4px solid var(--info-color); }
            .notification-success { border-left: 4px solid var(--success-color); }
            .notification-warning { border-left: 4px solid var(--warning-color); }
            .notification-error { border-left: 4px solid var(--error-color); }
            
            .notification-content {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
            }
            
            .notification-close {
                background: none;
                border: none;
                color: var(--text-muted);
                cursor: pointer;
                padding: var(--spacing-xs);
                border-radius: var(--radius-sm);
            }
            
            .notification-close:hover {
                background: var(--bg-tertiary);
            }
            
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }
}

function getNotificationIcon(type) {
    const icons = {
        'info': 'fa-info-circle',
        'success': 'fa-check-circle',
        'warning': 'fa-exclamation-triangle',
        'error': 'fa-times-circle'
    };
    return icons[type] || 'fa-info-circle';
}

// Utility Functions
function formatDateTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

function formatDate(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleDateString('ar-SA');
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('ar-SA');
}

// Logout Function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        logSecurityEvent('LOGOUT', 'User logged out', 'INFO', {
            timestamp: new Date().toISOString(),
            sessionDuration: getSessionDuration()
        });
        
        clearAuthData();
        window.location.href = 'login.html';
    }
}

function getSessionDuration() {
    const loginTime = localStorage.getItem('loginTime');
    if (loginTime) {
        return Math.floor((Date.now() - parseInt(loginTime)) / 1000);
    }
    return 0;
}

// Error Handling
window.addEventListener('error', function(event) {
    console.error('JavaScript Error:', event.error);
    logSecurityEvent('SYSTEM_ERROR', 'JavaScript error occurred', 'ERROR', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        timestamp: new Date().toISOString()
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (realTimeInterval) {
        clearInterval(realTimeInterval);
    }
});

// Security Headers Check
function checkSecurityHeaders() {
    // This would typically be done server-side, but we can simulate client-side checks
    const securityChecks = {
        https: location.protocol === 'https:',
        contentSecurityPolicy: document.querySelector('meta[http-equiv="Content-Security-Policy"]') !== null,
        xFrameOptions: true, // Would be checked via server response
        xContentTypeOptions: true // Would be checked via server response
    };
    
    const failedChecks = Object.entries(securityChecks)
        .filter(([key, value]) => !value)
        .map(([key]) => key);
    
    if (failedChecks.length > 0) {
        console.warn('Security headers missing:', failedChecks);
        logSecurityEvent('SECURITY_WARNING', 'Missing security headers detected', 'WARNING', {
            missingHeaders: failedChecks,
            timestamp: new Date().toISOString()
        });
    }
}

// Initialize security checks
document.addEventListener('DOMContentLoaded', function() {
    checkSecurityHeaders();
});

// Export functions for use in other scripts
window.HITSSecurity = {
    showNotification,
    formatDateTime,
    formatDate,
    formatTime,
    getEventTypeDisplay,
    getSeverityDisplay,
    toggleTheme,
    logout
};
