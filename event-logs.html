<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الأحداث - HITS Security</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>HITS Security</span>
                </div>
                <nav class="nav">
                    <a href="index.html" class="nav-link">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                    <a href="event-logs.html" class="nav-link active">
                        <i class="fas fa-list-alt"></i>
                        سجل الأحداث
                    </a>
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                </nav>
                <div class="header-actions">
                    <button class="theme-toggle" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="user-btn">
                            <i class="fas fa-user"></i>
                            المدير
                        </button>
                        <div class="dropdown">
                            <a href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Page Header -->
            <section class="page-header">
                <div class="page-title">
                    <h1>
                        <i class="fas fa-list-alt"></i>
                        سجل الأحداث الأمنية
                    </h1>
                    <p>عرض وإدارة جميع الأحداث الأمنية المسجلة في النظام</p>
                </div>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="exportEvents()">
                        <i class="fas fa-download"></i>
                        تصدير السجل
                    </button>
                    <button class="btn btn-secondary" onclick="refreshEvents()">
                        <i class="fas fa-sync"></i>
                        تحديث
                    </button>
                </div>
            </section>

            <!-- Filters Section -->
            <section class="filters-section">
                <div class="filters-card">
                    <div class="filters-header">
                        <h3>
                            <i class="fas fa-filter"></i>
                            تصفية الأحداث
                        </h3>
                        <button class="btn btn-secondary btn-sm" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                    <div class="filters-content">
                        <div class="filter-group">
                            <label for="severityFilter">مستوى الخطورة</label>
                            <select id="severityFilter" onchange="applyFilters()">
                                <option value="">جميع المستويات</option>
                                <option value="CRITICAL">حرج</option>
                                <option value="ERROR">خطأ</option>
                                <option value="WARNING">تحذير</option>
                                <option value="INFO">معلومات</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="categoryFilter">الفئة</label>
                            <select id="categoryFilter" onchange="applyFilters()">
                                <option value="">جميع الفئات</option>
                                <option value="AUTHENTICATION">المصادقة</option>
                                <option value="SECURITY">الأمان</option>
                                <option value="SYSTEM">النظام</option>
                                <option value="USER_MANAGEMENT">إدارة المستخدمين</option>
                                <option value="DATA_ACCESS">الوصول للبيانات</option>
                                <option value="NETWORK">الشبكة</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="userFilter">المستخدم</label>
                            <select id="userFilter" onchange="applyFilters()">
                                <option value="">جميع المستخدمين</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="dateFromFilter">من تاريخ</label>
                            <input type="datetime-local" id="dateFromFilter" onchange="applyFilters()">
                        </div>
                        <div class="filter-group">
                            <label for="dateToFilter">إلى تاريخ</label>
                            <input type="datetime-local" id="dateToFilter" onchange="applyFilters()">
                        </div>
                        <div class="filter-group">
                            <label for="searchFilter">البحث</label>
                            <input type="text" id="searchFilter" placeholder="البحث في الوصف أو النوع..." oninput="applyFilters()">
                        </div>
                    </div>
                </div>
            </section>

            <!-- Statistics Section -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalEvents">0</h3>
                            <p>إجمالي الأحداث</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="criticalEvents">0</h3>
                            <p>أحداث حرجة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="warningEvents">0</h3>
                            <p>تحذيرات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="todayEvents">0</h3>
                            <p>أحداث اليوم</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Events Table Section -->
            <section class="events-section">
                <div class="events-table-container">
                    <div class="table-header">
                        <div class="table-info">
                            <span id="eventsCount">0 حدث</span>
                            <span class="separator">|</span>
                            <span id="filteredInfo">عرض جميع الأحداث</span>
                        </div>
                        <div class="table-controls">
                            <select id="pageSize" onchange="changePageSize()">
                                <option value="10">10 لكل صفحة</option>
                                <option value="25" selected>25 لكل صفحة</option>
                                <option value="50">50 لكل صفحة</option>
                                <option value="100">100 لكل صفحة</option>
                            </select>
                        </div>
                    </div>
                    
                    <table class="events-table" id="eventsTable">
                        <thead>
                            <tr>
                                <th onclick="sortTable('timestamp')">
                                    الوقت
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortTable('severity')">
                                    الخطورة
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortTable('category')">
                                    الفئة
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortTable('type')">
                                    النوع
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortTable('user')">
                                    المستخدم
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th>الوصف</th>
                                <th>المصدر</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="eventsTableBody">
                            <!-- Events will be populated by JavaScript -->
                        </tbody>
                    </table>
                    
                    <div class="table-footer">
                        <div class="pagination" id="pagination">
                            <!-- Pagination will be generated by JavaScript -->
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Event Details Modal -->
    <div class="modal" id="eventDetailsModal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>تفاصيل الحدث</h3>
                <button class="close-btn" onclick="closeEventDetails()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="event-details" id="eventDetailsContent">
                    <!-- Event details will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeEventDetails()">إغلاق</button>
                <button class="btn btn-primary" onclick="exportSingleEvent()">
                    <i class="fas fa-download"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Event Modal -->
    <div class="modal" id="editEventModal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>تعديل الحدث</h3>
                <button class="close-btn" onclick="closeEditModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editEventForm" onsubmit="saveEventChanges(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editEventType">نوع الحدث</label>
                            <select id="editEventType" required>
                                <option value="">اختر نوع الحدث</option>
                                <optgroup label="المصادقة">
                                    <option value="LOGIN_SUCCESS">تسجيل دخول ناجح</option>
                                    <option value="LOGIN_FAILED">فشل تسجيل الدخول</option>
                                    <option value="LOGOUT">تسجيل خروج</option>
                                    <option value="PASSWORD_CHANGE">تغيير كلمة المرور</option>
                                </optgroup>
                                <optgroup label="الأمان">
                                    <option value="THREAT_DETECTED">تهديد مكتشف</option>
                                    <option value="MALWARE_DETECTED">برمجية خبيثة</option>
                                    <option value="SUSPICIOUS_ACTIVITY">نشاط مشبوه</option>
                                    <option value="UNAUTHORIZED_ACCESS">وصول غير مصرح</option>
                                    <option value="DATA_BREACH_ATTEMPT">محاولة اختراق البيانات</option>
                                </optgroup>
                                <optgroup label="النظام">
                                    <option value="SYSTEM_UPDATE">تحديث النظام</option>
                                    <option value="SYSTEM_ERROR">خطأ في النظام</option>
                                    <option value="CONFIGURATION_CHANGE">تغيير الإعدادات</option>
                                    <option value="DATA_BACKUP">نسخ احتياطي</option>
                                </optgroup>
                                <optgroup label="الشبكة">
                                    <option value="FIREWALL_BLOCK">حظر جدار الحماية</option>
                                    <option value="NETWORK_ERROR">خطأ في الشبكة</option>
                                    <option value="PORT_SCAN_DETECTED">مسح المنافذ</option>
                                </optgroup>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="editEventSeverity">مستوى الخطورة</label>
                            <select id="editEventSeverity" required>
                                <option value="">اختر مستوى الخطورة</option>
                                <option value="INFO">معلومات</option>
                                <option value="WARNING">تحذير</option>
                                <option value="ERROR">خطأ</option>
                                <option value="CRITICAL">حرج</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editEventSource">المصدر</label>
                            <input type="text" id="editEventSource" required>
                        </div>

                        <div class="form-group">
                            <label for="editEventIP">عنوان IP</label>
                            <input type="text" id="editEventIP" pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="editEventDescription">وصف الحدث</label>
                        <textarea id="editEventDescription" rows="3" required></textarea>
                    </div>

                    <input type="hidden" id="editEventId">
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeEditModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="saveEventChanges(event)">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" id="deleteConfirmModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تأكيد الحذف</h3>
                <button class="close-btn" onclick="closeDeleteModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>هل أنت متأكد من حذف هذا الحدث؟</p>
                    <p class="warning-text">لا يمكن التراجع عن هذا الإجراء!</p>
                    <div class="event-preview" id="deleteEventPreview">
                        <!-- Event details will be shown here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeDeleteModal()">إلغاء</button>
                <button class="btn btn-error" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i>
                    حذف الحدث
                </button>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal" id="exportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تصدير سجل الأحداث</h3>
                <button class="close-btn" onclick="closeExportModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="exportFormat">تنسيق التصدير</label>
                    <select id="exportFormat">
                        <option value="json">JSON</option>
                        <option value="csv">CSV</option>
                        <option value="xml">XML</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="exportFiltered" checked>
                        تصدير الأحداث المفلترة فقط
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="includeMetadata">
                        تضمين البيانات الوصفية
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeExportModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="performExport()">
                    <i class="fas fa-download"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 HITS Security System. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
    <script src="auth.js"></script>
    <script src="event-logger.js"></script>
    <script>
        // Page-specific variables
        let currentPage = 1;
        let pageSize = 25;
        let sortColumn = 'timestamp';
        let sortDirection = 'desc';
        let currentFilters = {};
        let allEvents = [];
        let filteredEvents = [];
        let selectedEvent = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            initializeEventLogs();
            loadEvents();
            populateUserFilter();
            updateStatistics();
        });

        function initializeEventLogs() {
            // Set default date filters (last 7 days)
            const now = new Date();
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            document.getElementById('dateFromFilter').value = formatDateTimeLocal(weekAgo);
            document.getElementById('dateToFilter').value = formatDateTimeLocal(now);
        }

        function loadEvents() {
            allEvents = getSecurityEvents();
            applyFilters();
        }

        function applyFilters() {
            const filters = {
                severity: document.getElementById('severityFilter').value,
                category: document.getElementById('categoryFilter').value,
                user: document.getElementById('userFilter').value,
                startDate: document.getElementById('dateFromFilter').value,
                endDate: document.getElementById('dateToFilter').value,
                search: document.getElementById('searchFilter').value
            };

            currentFilters = filters;
            filteredEvents = getSecurityEvents(filters);
            
            currentPage = 1;
            updateTable();
            updateStatistics();
            updatePagination();
        }

        function updateTable() {
            const tbody = document.getElementById('eventsTableBody');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageEvents = filteredEvents.slice(startIndex, endIndex);

            tbody.innerHTML = pageEvents.map(event => `
                <tr onclick="showEventDetails('${event.id}')" class="event-row">
                    <td>${formatDateTime(event.timestamp)}</td>
                    <td>
                        <span class="status-badge ${event.severity.toLowerCase()}">
                            <i class="fas ${getSeverityIcon(event.severity)}"></i>
                            ${getSeverityDisplay(event.severity)}
                        </span>
                    </td>
                    <td>${getCategoryDisplay(event.category)}</td>
                    <td>${getEventTypeDisplay(event.type)}</td>
                    <td>${event.user}</td>
                    <td class="description-cell" title="${event.description}">
                        ${truncateText(event.description, 50)}
                    </td>
                    <td>${event.source}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-secondary" onclick="event.stopPropagation(); showEventDetails('${event.id}')" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); editEvent('${event.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-error" onclick="event.stopPropagation(); deleteEvent('${event.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');

            // Update table info
            document.getElementById('eventsCount').textContent = `${filteredEvents.length} حدث`;
            document.getElementById('filteredInfo').textContent =
                filteredEvents.length === allEvents.length ?
                'عرض جميع الأحداث' :
                `مفلتر من ${allEvents.length} حدث`;
        }

        function updateStatistics() {
            const stats = getSecurityEventStatistics('24h');
            
            document.getElementById('totalEvents').textContent = allEvents.length;
            document.getElementById('criticalEvents').textContent = stats.bySeverity.CRITICAL || 0;
            document.getElementById('warningEvents').textContent = stats.bySeverity.WARNING || 0;
            
            // Calculate today's events
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const todayEvents = allEvents.filter(event => 
                new Date(event.timestamp) >= today
            );
            document.getElementById('todayEvents').textContent = todayEvents.length;
        }

        function updatePagination() {
            const totalPages = Math.ceil(filteredEvents.length / pageSize);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';
            
            // Previous button
            if (currentPage > 1) {
                paginationHTML += `<button class="page-btn" onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>`;
            }

            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHTML += `<button class="page-btn" onclick="changePage(1)">1</button>`;
                if (startPage > 2) {
                    paginationHTML += `<span class="page-ellipsis">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `<button class="page-btn ${i === currentPage ? 'active' : ''}" 
                    onclick="changePage(${i})">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHTML += `<span class="page-ellipsis">...</span>`;
                }
                paginationHTML += `<button class="page-btn" onclick="changePage(${totalPages})">${totalPages}</button>`;
            }

            // Next button
            if (currentPage < totalPages) {
                paginationHTML += `<button class="page-btn" onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>`;
            }

            pagination.innerHTML = paginationHTML;
        }

        function changePage(page) {
            currentPage = page;
            updateTable();
            updatePagination();
        }

        function changePageSize() {
            pageSize = parseInt(document.getElementById('pageSize').value);
            currentPage = 1;
            updateTable();
            updatePagination();
        }

        function sortTable(column) {
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'desc';
            }

            filteredEvents.sort((a, b) => {
                let aValue = a[column];
                let bValue = b[column];

                if (column === 'timestamp') {
                    aValue = new Date(aValue);
                    bValue = new Date(bValue);
                }

                if (sortDirection === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });

            updateTable();
            updateSortIcons();
        }

        function updateSortIcons() {
            document.querySelectorAll('th i.fas').forEach(icon => {
                icon.className = 'fas fa-sort';
            });

            const currentHeader = document.querySelector(`th[onclick="sortTable('${sortColumn}')"] i`);
            if (currentHeader) {
                currentHeader.className = `fas fa-sort-${sortDirection === 'asc' ? 'up' : 'down'}`;
            }
        }

        function showEventDetails(eventId) {
            selectedEvent = allEvents.find(event => event.id === eventId);
            if (!selectedEvent) return;

            const content = document.getElementById('eventDetailsContent');
            content.innerHTML = `
                <div class="event-detail-grid">
                    <div class="detail-item">
                        <label>معرف الحدث:</label>
                        <span>${selectedEvent.id}</span>
                    </div>
                    <div class="detail-item">
                        <label>الوقت:</label>
                        <span>${formatDateTime(selectedEvent.timestamp)}</span>
                    </div>
                    <div class="detail-item">
                        <label>النوع:</label>
                        <span>${getEventTypeDisplay(selectedEvent.type)}</span>
                    </div>
                    <div class="detail-item">
                        <label>الفئة:</label>
                        <span>${getCategoryDisplay(selectedEvent.category)}</span>
                    </div>
                    <div class="detail-item">
                        <label>مستوى الخطورة:</label>
                        <span class="status-badge ${selectedEvent.severity.toLowerCase()}">
                            ${getSeverityDisplay(selectedEvent.severity)}
                        </span>
                    </div>
                    <div class="detail-item">
                        <label>المستخدم:</label>
                        <span>${selectedEvent.user}</span>
                    </div>
                    <div class="detail-item">
                        <label>المصدر:</label>
                        <span>${selectedEvent.source}</span>
                    </div>
                    <div class="detail-item">
                        <label>عنوان IP:</label>
                        <span>${selectedEvent.ip}</span>
                    </div>
                    <div class="detail-item full-width">
                        <label>الوصف:</label>
                        <span>${selectedEvent.description}</span>
                    </div>
                </div>
            `;

            document.getElementById('eventDetailsModal').style.display = 'flex';
        }

        function closeEventDetails() {
            document.getElementById('eventDetailsModal').style.display = 'none';
            selectedEvent = null;
        }

        function exportEvents() {
            document.getElementById('exportModal').style.display = 'flex';
        }

        function closeExportModal() {
            document.getElementById('exportModal').style.display = 'none';
        }

        function performExport() {
            const format = document.getElementById('exportFormat').value;
            const exportFiltered = document.getElementById('exportFiltered').checked;
            const includeMetadata = document.getElementById('includeMetadata').checked;

            const eventsToExport = exportFiltered ? filteredEvents : allEvents;
            
            try {
                const exportData = exportSecurityEvents(format, {
                    events: eventsToExport,
                    includeMetadata: includeMetadata
                });

                downloadFile(exportData, `security-events-${new Date().toISOString().split('T')[0]}.${format}`);
                
                showNotification('تم تصدير السجل بنجاح', 'success');
                closeExportModal();

                logSecurityEvent('DATA_EXPORT', `Security events exported in ${format} format`, 'INFO', {
                    format: format,
                    eventCount: eventsToExport.length,
                    includeMetadata: includeMetadata
                });
            } catch (error) {
                showNotification('حدث خطأ أثناء التصدير', 'error');
                console.error('Export error:', error);
            }
        }

        function exportSingleEvent() {
            if (!selectedEvent) return;

            const exportData = exportSecurityEvents('json', {
                events: [selectedEvent],
                includeMetadata: true
            });

            downloadFile(exportData, `event-${selectedEvent.id}.json`);
            showNotification('تم تصدير الحدث بنجاح', 'success');
        }

        function downloadFile(content, filename) {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function refreshEvents() {
            loadEvents();
            showNotification('تم تحديث السجل', 'info');
        }

        function clearFilters() {
            document.getElementById('severityFilter').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('userFilter').value = '';
            document.getElementById('dateFromFilter').value = '';
            document.getElementById('dateToFilter').value = '';
            document.getElementById('searchFilter').value = '';
            
            applyFilters();
        }

        function populateUserFilter() {
            const users = [...new Set(allEvents.map(event => event.user))];
            const userFilter = document.getElementById('userFilter');
            
            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user;
                option.textContent = user;
                userFilter.appendChild(option);
            });
        }

        // Utility functions
        function formatDateTimeLocal(date) {
            const d = new Date(date);
            d.setMinutes(d.getMinutes() - d.getTimezoneOffset());
            return d.toISOString().slice(0, 16);
        }

        function getSeverityIcon(severity) {
            const icons = {
                'INFO': 'fa-info-circle',
                'WARNING': 'fa-exclamation-triangle',
                'ERROR': 'fa-times-circle',
                'CRITICAL': 'fa-exclamation-circle'
            };
            return icons[severity] || 'fa-info-circle';
        }

        function getCategoryDisplay(category) {
            const categories = {
                'AUTHENTICATION': 'المصادقة',
                'SECURITY': 'الأمان',
                'SYSTEM': 'النظام',
                'USER_MANAGEMENT': 'إدارة المستخدمين',
                'DATA_ACCESS': 'الوصول للبيانات',
                'NETWORK': 'الشبكة'
            };
            return categories[category] || category;
        }

        function truncateText(text, maxLength) {
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        // Edit Event Functions
        let currentEditingEvent = null;

        function editEvent(eventId) {
            const event = allEvents.find(e => e.id === eventId);
            if (!event) {
                showNotification('الحدث غير موجود', 'error');
                return;
            }

            currentEditingEvent = event;

            // Fill the edit form
            document.getElementById('editEventId').value = event.id;
            document.getElementById('editEventType').value = event.type;
            document.getElementById('editEventSeverity').value = event.severity;
            document.getElementById('editEventSource').value = event.source;
            document.getElementById('editEventIP').value = event.ip || '';
            document.getElementById('editEventDescription').value = event.description;

            // Show the modal
            document.getElementById('editEventModal').style.display = 'flex';
        }

        function closeEditModal() {
            document.getElementById('editEventModal').style.display = 'none';
            currentEditingEvent = null;
        }

        function saveEventChanges(e) {
            if (e) e.preventDefault();

            if (!currentEditingEvent) {
                showNotification('لا يوجد حدث للتعديل', 'error');
                return;
            }

            // Get form data
            const eventType = document.getElementById('editEventType').value;
            const eventSeverity = document.getElementById('editEventSeverity').value;
            const eventSource = document.getElementById('editEventSource').value;
            const eventIP = document.getElementById('editEventIP').value;
            const eventDescription = document.getElementById('editEventDescription').value;

            // Validate required fields
            if (!eventType || !eventSeverity || !eventSource || !eventDescription) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // Preserve existing metadata and add edit information
            let metadata = currentEditingEvent.metadata || {};
            metadata.lastModified = new Date().toISOString();
            metadata.modifiedBy = getCurrentUser()?.username || 'unknown';
            metadata.originalTimestamp = currentEditingEvent.timestamp;

            // Update the event
            const eventIndex = allEvents.findIndex(e => e.id === currentEditingEvent.id);
            if (eventIndex !== -1) {
                allEvents[eventIndex] = {
                    ...currentEditingEvent,
                    type: eventType,
                    category: getEventCategory(eventType),
                    severity: eventSeverity,
                    source: eventSource,
                    ip: eventIP || currentEditingEvent.ip,
                    description: eventDescription,
                    metadata: metadata
                };

                // Update in event logger
                if (window.HITSEventLogger && window.HITSEventLogger.eventLogger) {
                    const eventLoggerInstance = window.HITSEventLogger.eventLogger;
                    const loggerEventIndex = eventLoggerInstance.events.findIndex(e => e.id === currentEditingEvent.id);
                    if (loggerEventIndex !== -1) {
                        eventLoggerInstance.events[loggerEventIndex] = allEvents[eventIndex];
                        eventLoggerInstance.saveEvents();
                    }
                }

                // Log the edit action
                logSecurityEvent('EVENT_MODIFIED', `Event ${currentEditingEvent.id} was modified`, 'INFO', {
                    originalEvent: currentEditingEvent.id,
                    modifiedFields: {
                        type: eventType,
                        severity: eventSeverity,
                        source: eventSource,
                        description: eventDescription
                    },
                    timestamp: new Date().toISOString()
                });

                showNotification('تم تحديث الحدث بنجاح', 'success');
                closeEditModal();

                // Refresh the table
                applyFilters();
            } else {
                showNotification('حدث خطأ أثناء تحديث الحدث', 'error');
            }
        }

        // Delete Event Functions
        let currentDeletingEventId = null;

        function deleteEvent(eventId) {
            const event = allEvents.find(e => e.id === eventId);
            if (!event) {
                showNotification('الحدث غير موجود', 'error');
                return;
            }

            currentDeletingEventId = eventId;

            // Show event preview in delete modal
            const preview = document.getElementById('deleteEventPreview');
            preview.innerHTML = `
                <div class="event-preview-item">
                    <strong>النوع:</strong> ${getEventTypeDisplay(event.type)}
                </div>
                <div class="event-preview-item">
                    <strong>الخطورة:</strong> <span class="status-badge ${event.severity.toLowerCase()}">${getSeverityDisplay(event.severity)}</span>
                </div>
                <div class="event-preview-item">
                    <strong>الوقت:</strong> ${formatDateTime(event.timestamp)}
                </div>
                <div class="event-preview-item">
                    <strong>الوصف:</strong> ${event.description}
                </div>
            `;

            // Show the modal
            document.getElementById('deleteConfirmModal').style.display = 'flex';
        }

        function closeDeleteModal() {
            document.getElementById('deleteConfirmModal').style.display = 'none';
            currentDeletingEventId = null;
        }

        function confirmDelete() {
            if (!currentDeletingEventId) {
                showNotification('لا يوجد حدث للحذف', 'error');
                return;
            }

            const eventIndex = allEvents.findIndex(e => e.id === currentDeletingEventId);
            if (eventIndex !== -1) {
                const deletedEvent = allEvents[eventIndex];

                // Remove from allEvents array
                allEvents.splice(eventIndex, 1);

                // Remove from event logger
                if (window.HITSEventLogger && window.HITSEventLogger.eventLogger) {
                    const eventLoggerInstance = window.HITSEventLogger.eventLogger;
                    const loggerEventIndex = eventLoggerInstance.events.findIndex(e => e.id === currentDeletingEventId);
                    if (loggerEventIndex !== -1) {
                        eventLoggerInstance.events.splice(loggerEventIndex, 1);
                        eventLoggerInstance.saveEvents();
                    }
                }

                // Log the delete action
                logSecurityEvent('EVENT_DELETED', `Event ${currentDeletingEventId} was deleted`, 'WARNING', {
                    deletedEvent: {
                        id: deletedEvent.id,
                        type: deletedEvent.type,
                        severity: deletedEvent.severity,
                        timestamp: deletedEvent.timestamp,
                        description: deletedEvent.description
                    },
                    deletedBy: getCurrentUser()?.username || 'unknown',
                    deletedAt: new Date().toISOString()
                });

                showNotification('تم حذف الحدث بنجاح', 'success');
                closeDeleteModal();

                // Refresh the table
                applyFilters();
                updateStatistics();
            } else {
                showNotification('حدث خطأ أثناء حذف الحدث', 'error');
            }
        }

        // Helper function to get event category
        function getEventCategory(type) {
            const categories = {
                'LOGIN_SUCCESS': 'AUTHENTICATION',
                'LOGIN_FAILED': 'AUTHENTICATION',
                'LOGOUT': 'AUTHENTICATION',
                'PASSWORD_CHANGE': 'AUTHENTICATION',
                'THREAT_DETECTED': 'SECURITY',
                'MALWARE_DETECTED': 'SECURITY',
                'SUSPICIOUS_ACTIVITY': 'SECURITY',
                'UNAUTHORIZED_ACCESS': 'SECURITY',
                'DATA_BREACH_ATTEMPT': 'SECURITY',
                'SYSTEM_UPDATE': 'SYSTEM',
                'SYSTEM_ERROR': 'SYSTEM',
                'CONFIGURATION_CHANGE': 'SYSTEM',
                'DATA_BACKUP': 'SYSTEM',
                'FIREWALL_BLOCK': 'NETWORK',
                'NETWORK_ERROR': 'NETWORK',
                'PORT_SCAN_DETECTED': 'NETWORK'
            };
            return categories[type] || 'UNKNOWN';
        }
    </script>
</body>
</html>
