// Dashboard Charts and Visualization Module

let eventsChart;
let threatDistributionChart;
let currentTimeRange = '1h';
let chartUpdateInterval;

// Chart Configuration
const chartConfig = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            labels: {
                usePointStyle: true,
                font: {
                    family: 'Cairo',
                    size: 12
                }
            }
        }
    },
    scales: {
        x: {
            grid: {
                color: 'rgba(100, 116, 139, 0.1)'
            },
            ticks: {
                font: {
                    family: 'Cairo',
                    size: 11
                }
            }
        },
        y: {
            grid: {
                color: 'rgba(100, 116, 139, 0.1)'
            },
            ticks: {
                font: {
                    family: 'Cairo',
                    size: 11
                }
            }
        }
    }
};

// Initialize Charts
function initializeCharts() {
    initializeEventsChart();
    initializeThreatDistributionChart();
    updateChartData();
}

function initializeEventsChart() {
    const ctx = document.getElementById('eventsChart').getContext('2d');
    
    eventsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'حرج',
                    data: [],
                    borderColor: '#dc2626',
                    backgroundColor: 'rgba(220, 38, 38, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'خطأ',
                    data: [],
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'تحذير',
                    data: [],
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'معلومات',
                    data: [],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }
            ]
        },
        options: {
            ...chartConfig,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                ...chartConfig.plugins,
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleFont: {
                        family: 'Cairo'
                    },
                    bodyFont: {
                        family: 'Cairo'
                    }
                }
            }
        }
    });
}

function initializeThreatDistributionChart() {
    const ctx = document.getElementById('threatDistributionChart').getContext('2d');
    
    threatDistributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['محاولات اختراق', 'برمجيات خبيثة', 'نشاط مشبوه', 'فشل المصادقة', 'أخرى'],
            datasets: [{
                data: [0, 0, 0, 0, 0],
                backgroundColor: [
                    '#dc2626',
                    '#f59e0b',
                    '#8b5cf6',
                    '#ef4444',
                    '#64748b'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        font: {
                            family: 'Cairo',
                            size: 12
                        },
                        padding: 15
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleFont: {
                        family: 'Cairo'
                    },
                    bodyFont: {
                        family: 'Cairo'
                    }
                }
            }
        }
    });
}

// Update Chart Data
function updateChartData() {
    const stats = getSecurityEventStatistics(currentTimeRange);
    updateEventsChart(stats);
    updateThreatDistributionChart(stats);
}

function updateEventsChart(stats) {
    if (!eventsChart || !stats.timeline) return;
    
    const labels = stats.timeline.map(point => point.time);
    const criticalData = stats.timeline.map(point => point.critical || 0);
    const errorData = stats.timeline.map(point => point.error || 0);
    const warningData = stats.timeline.map(point => point.warning || 0);
    const infoData = stats.timeline.map(point => point.info || 0);
    
    eventsChart.data.labels = labels;
    eventsChart.data.datasets[0].data = criticalData;
    eventsChart.data.datasets[1].data = errorData;
    eventsChart.data.datasets[2].data = warningData;
    eventsChart.data.datasets[3].data = infoData;
    
    eventsChart.update('none');
}

function updateThreatDistributionChart(stats) {
    if (!threatDistributionChart) return;
    
    // Calculate threat distribution based on event types
    const events = getSecurityEvents({
        startDate: new Date(Date.now() - getTimeRangeMs(currentTimeRange)).toISOString()
    });
    
    const threatCounts = {
        intrusion: 0,
        malware: 0,
        suspicious: 0,
        authFailure: 0,
        other: 0
    };
    
    events.forEach(event => {
        switch (event.type) {
            case 'INTRUSION_ATTEMPT':
            case 'DATA_BREACH_ATTEMPT':
            case 'UNAUTHORIZED_ACCESS':
                threatCounts.intrusion++;
                break;
            case 'MALWARE_DETECTED':
            case 'THREAT_DETECTED':
                threatCounts.malware++;
                break;
            case 'SUSPICIOUS_ACTIVITY':
            case 'PRIVILEGE_ESCALATION':
                threatCounts.suspicious++;
                break;
            case 'LOGIN_FAILED':
            case 'ACCOUNT_LOCKED':
                threatCounts.authFailure++;
                break;
            default:
                threatCounts.other++;
        }
    });
    
    threatDistributionChart.data.datasets[0].data = [
        threatCounts.intrusion,
        threatCounts.malware,
        threatCounts.suspicious,
        threatCounts.authFailure,
        threatCounts.other
    ];
    
    threatDistributionChart.update('none');
}

// Time Range Functions
function changeTimeRange(range) {
    currentTimeRange = range;
    
    // Update active button
    document.querySelectorAll('.chart-controls button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    updateChartData();
    
    logSecurityEvent('DASHBOARD_VIEW', `Time range changed to ${range}`, 'INFO', {
        timeRange: range,
        timestamp: new Date().toISOString()
    });
}

function getTimeRangeMs(range) {
    switch (range) {
        case '1h': return 60 * 60 * 1000;
        case '6h': return 6 * 60 * 60 * 1000;
        case '24h': return 24 * 60 * 60 * 1000;
        case '7d': return 7 * 24 * 60 * 60 * 1000;
        default: return 60 * 60 * 1000;
    }
}

// Security Dashboard Functions
function initializeSecurityDashboard() {
    updateThreatLevel();
    updateSecurityMetrics();
    loadThreatSources();
}

function updateThreatLevel() {
    const events = getSecurityEvents({
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    });
    
    const criticalEvents = events.filter(e => e.severity === 'CRITICAL').length;
    const errorEvents = events.filter(e => e.severity === 'ERROR').length;
    const warningEvents = events.filter(e => e.severity === 'WARNING').length;
    
    let threatLevel = 'منخفض';
    let threatPercentage = 20;
    let threatColor = '#10b981';
    
    if (criticalEvents > 5) {
        threatLevel = 'حرج';
        threatPercentage = 90;
        threatColor = '#dc2626';
    } else if (criticalEvents > 2 || errorEvents > 10) {
        threatLevel = 'عالي';
        threatPercentage = 70;
        threatColor = '#f59e0b';
    } else if (errorEvents > 5 || warningEvents > 20) {
        threatLevel = 'متوسط';
        threatPercentage = 50;
        threatColor = '#f59e0b';
    }
    
    document.getElementById('threatLevel').textContent = threatLevel;
    
    const threatBar = document.getElementById('threatBar');
    threatBar.style.width = threatPercentage + '%';
    threatBar.style.backgroundColor = threatColor;
}

function updateSecurityMetrics() {
    // Simulate security metrics
    const metrics = {
        activeProtections: Math.floor(Math.random() * 5) + 10,
        vulnerabilities: Math.floor(Math.random() * 8),
        lastScan: getRandomTimeAgo()
    };
    
    document.getElementById('activeProtections').textContent = metrics.activeProtections;
    document.getElementById('vulnerabilities').textContent = metrics.vulnerabilities;
    document.getElementById('lastScan').textContent = metrics.lastScan;
}

function loadSecurityAlerts() {
    const alertsContainer = document.getElementById('alertsContainer');
    const recentEvents = getSecurityEvents({
        severity: ['CRITICAL', 'ERROR', 'WARNING'],
        limit: 10
    });
    
    if (recentEvents.length === 0) {
        alertsContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                لا توجد تنبيهات أمنية حالياً
            </div>
        `;
        return;
    }
    
    alertsContainer.innerHTML = recentEvents.map(event => `
        <div class="alert alert-${event.severity.toLowerCase()}">
            <div class="alert-content">
                <div class="alert-header">
                    <i class="fas ${getSeverityIcon(event.severity)}"></i>
                    <span class="alert-title">${getEventTypeDisplay(event.type)}</span>
                    <span class="alert-time">${formatDateTime(event.timestamp)}</span>
                </div>
                <div class="alert-description">
                    ${event.description}
                </div>
                <div class="alert-source">
                    المصدر: ${event.source} | المستخدم: ${event.user}
                </div>
            </div>
            <div class="alert-actions">
                <button class="btn btn-sm btn-secondary" onclick="viewEventDetails('${event.id}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-secondary" onclick="dismissAlert('${event.id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function loadThreatSources() {
    const threatSources = document.getElementById('threatSources');
    
    // Simulate threat source data
    const sources = [
        { ip: '*************', country: 'محلي', threats: 5, blocked: true },
        { ip: '************', country: 'غير معروف', threats: 12, blocked: true },
        { ip: '*************', country: 'خارجي', threats: 3, blocked: false },
        { ip: '*********', country: 'محلي', threats: 2, blocked: false }
    ];
    
    threatSources.innerHTML = sources.map(source => `
        <div class="threat-source-item">
            <div class="source-info">
                <div class="source-ip">${source.ip}</div>
                <div class="source-country">${source.country}</div>
            </div>
            <div class="source-stats">
                <span class="threat-count">${source.threats} تهديد</span>
                <span class="source-status ${source.blocked ? 'blocked' : 'allowed'}">
                    ${source.blocked ? 'محظور' : 'مسموح'}
                </span>
            </div>
            <div class="source-actions">
                <button class="btn btn-sm ${source.blocked ? 'btn-success' : 'btn-error'}" 
                        onclick="toggleSourceBlock('${source.ip}', ${!source.blocked})">
                    ${source.blocked ? 'إلغاء الحظر' : 'حظر'}
                </button>
            </div>
        </div>
    `).join('');
}

// Dashboard Actions
function generateSecurityReport() {
    showNotification('جاري إنشاء التقرير الأمني...', 'info');
    
    setTimeout(() => {
        const reportData = {
            timestamp: new Date().toISOString(),
            threatLevel: document.getElementById('threatLevel').textContent,
            activeProtections: document.getElementById('activeProtections').textContent,
            vulnerabilities: document.getElementById('vulnerabilities').textContent,
            recentEvents: getSecurityEvents({ limit: 50 }),
            statistics: getSecurityEventStatistics('24h')
        };
        
        const reportContent = generateReportContent(reportData);
        downloadFile(reportContent, `security-report-${new Date().toISOString().split('T')[0]}.txt`);
        
        showNotification('تم إنشاء التقرير الأمني بنجاح', 'success');
        
        logSecurityEvent('REPORT_GENERATED', 'Security dashboard report generated', 'INFO', {
            reportType: 'dashboard_summary',
            timestamp: new Date().toISOString()
        });
    }, 2000);
}

function refreshDashboard() {
    showNotification('جاري تحديث لوحة الأمان...', 'info');
    
    updateThreatLevel();
    updateSecurityMetrics();
    loadSecurityAlerts();
    loadThreatSources();
    updateChartData();
    
    showNotification('تم تحديث لوحة الأمان', 'success');
    
    logSecurityEvent('DASHBOARD_REFRESH', 'Security dashboard refreshed', 'INFO', {
        timestamp: new Date().toISOString()
    });
}

function markAllAlertsRead() {
    showNotification('تم تعيين جميع التنبيهات كمقروءة', 'success');
    
    logSecurityEvent('ALERTS_MARKED_READ', 'All security alerts marked as read', 'INFO', {
        timestamp: new Date().toISOString()
    });
}

function viewEventDetails(eventId) {
    // This would typically open a modal or navigate to event details
    window.location.href = `event-logs.html#event-${eventId}`;
}

function dismissAlert(eventId) {
    const alertElement = event.target.closest('.alert');
    if (alertElement) {
        alertElement.style.opacity = '0';
        setTimeout(() => {
            alertElement.remove();
        }, 300);
    }
    
    logSecurityEvent('ALERT_DISMISSED', `Alert dismissed for event ${eventId}`, 'INFO', {
        eventId: eventId,
        timestamp: new Date().toISOString()
    });
}

function toggleSourceBlock(ip, block) {
    const action = block ? 'حظر' : 'إلغاء حظر';
    showNotification(`تم ${action} العنوان ${ip}`, 'success');
    
    logSecurityEvent('IP_BLOCK_TOGGLE', `IP ${ip} ${block ? 'blocked' : 'unblocked'}`, 'WARNING', {
        ip: ip,
        action: block ? 'block' : 'unblock',
        timestamp: new Date().toISOString()
    });
    
    // Refresh threat sources
    setTimeout(() => {
        loadThreatSources();
    }, 1000);
}

// Real-time Monitoring
function startRealTimeMonitoring() {
    // Update dashboard every 30 seconds
    chartUpdateInterval = setInterval(() => {
        updateChartData();
        updateThreatLevel();
        loadSecurityAlerts();
    }, 30000);
    
    // Simulate real-time events
    setInterval(() => {
        if (Math.random() < 0.3) { // 30% chance every 10 seconds
            generateRandomSecurityEvent();
        }
    }, 10000);
}

function generateRandomSecurityEvent() {
    const eventTypes = [
        'LOGIN_SUCCESS', 'LOGIN_FAILED', 'SUSPICIOUS_ACTIVITY', 
        'FIREWALL_BLOCK', 'SYSTEM_UPDATE', 'DATA_ACCESS'
    ];
    
    const severities = ['INFO', 'WARNING', 'ERROR'];
    const randomType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
    const randomSeverity = severities[Math.floor(Math.random() * severities.length)];
    
    logSecurityEvent(randomType, `Simulated ${randomType} event`, randomSeverity, {
        simulated: true,
        timestamp: new Date().toISOString()
    });
}

// Utility Functions
function generateReportContent(data) {
    return `
تقرير الأمان - لوحة التحكم
============================

تاريخ الإنشاء: ${formatDateTime(data.timestamp)}

الحالة العامة:
- مستوى التهديد: ${data.threatLevel}
- الحمايات النشطة: ${data.activeProtections}
- الثغرات المكتشفة: ${data.vulnerabilities}

إحصائيات الأحداث (آخر 24 ساعة):
- إجمالي الأحداث: ${data.statistics.total}
- أحداث حرجة: ${data.statistics.bySeverity.CRITICAL || 0}
- أحداث خطأ: ${data.statistics.bySeverity.ERROR || 0}
- تحذيرات: ${data.statistics.bySeverity.WARNING || 0}

الأحداث الأخيرة:
${data.recentEvents.slice(0, 10).map(event => 
    `- ${formatDateTime(event.timestamp)}: ${getEventTypeDisplay(event.type)} (${getSeverityDisplay(event.severity)})`
).join('\n')}

---
تم إنشاء هذا التقرير بواسطة نظام HITS Security
    `.trim();
}

function getRandomTimeAgo() {
    const timeOptions = ['منذ دقائق', 'منذ ساعة', 'منذ ساعتين', 'منذ 3 ساعات', 'منذ يوم'];
    return timeOptions[Math.floor(Math.random() * timeOptions.length)];
}

function getSeverityIcon(severity) {
    const icons = {
        'INFO': 'fa-info-circle',
        'WARNING': 'fa-exclamation-triangle',
        'ERROR': 'fa-times-circle',
        'CRITICAL': 'fa-exclamation-circle'
    };
    return icons[severity] || 'fa-info-circle';
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (chartUpdateInterval) {
        clearInterval(chartUpdateInterval);
    }
});

// Export functions for global use
window.HITSDashboard = {
    initializeCharts,
    updateChartData,
    changeTimeRange,
    generateSecurityReport,
    refreshDashboard,
    startRealTimeMonitoring
};
