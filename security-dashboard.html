<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الأمان - HITS Security</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>HITS Security</span>
                </div>
                <nav class="nav">
                    <a href="index.html" class="nav-link">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                    <a href="security-dashboard.html" class="nav-link active">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة الأمان
                    </a>
                    <a href="event-logs.html" class="nav-link">
                        <i class="fas fa-list-alt"></i>
                        سجل الأحداث
                    </a>
                    <a href="settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                </nav>
                <div class="header-actions">
                    <button class="theme-toggle" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="user-btn">
                            <i class="fas fa-user"></i>
                            المدير
                        </button>
                        <div class="dropdown">
                            <a href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Page Header -->
            <section class="page-header">
                <div class="page-title">
                    <h1>
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة الأمان المتقدمة
                    </h1>
                    <p>مراقبة شاملة للحالة الأمنية والتهديدات في الوقت الفعلي</p>
                </div>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="generateSecurityReport()">
                        <i class="fas fa-file-alt"></i>
                        تقرير أمني
                    </button>
                    <button class="btn btn-secondary" onclick="refreshDashboard()">
                        <i class="fas fa-sync"></i>
                        تحديث
                    </button>
                </div>
            </section>

            <!-- Security Status Cards -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card threat-level">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="threatLevel">منخفض</h3>
                            <p>مستوى التهديد</p>
                            <div class="threat-indicator">
                                <div class="threat-bar" id="threatBar"></div>
                            </div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="activeProtections">12</h3>
                            <p>الحمايات النشطة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-bug"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="vulnerabilities">3</h3>
                            <p>الثغرات المكتشفة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="lastScan">منذ ساعتين</h3>
                            <p>آخر فحص</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Real-time Monitoring -->
            <section class="chart-container">
                <div class="chart-header">
                    <h3>
                        <i class="fas fa-chart-line"></i>
                        مراقبة الأحداث في الوقت الفعلي
                    </h3>
                    <div class="chart-controls">
                        <button class="active" onclick="changeTimeRange('1h')">ساعة</button>
                        <button onclick="changeTimeRange('6h')">6 ساعات</button>
                        <button onclick="changeTimeRange('24h')">24 ساعة</button>
                        <button onclick="changeTimeRange('7d')">أسبوع</button>
                    </div>
                </div>
                <div class="chart-area">
                    <canvas id="eventsChart"></canvas>
                </div>
            </section>

            <!-- Security Alerts -->
            <section class="alerts-section">
                <div class="section-header">
                    <h2>
                        <i class="fas fa-bell"></i>
                        التنبيهات الأمنية
                    </h2>
                    <button class="btn btn-secondary btn-sm" onclick="markAllAlertsRead()">
                        <i class="fas fa-check"></i>
                        تعيين الكل كمقروء
                    </button>
                </div>
                <div class="alerts-container" id="alertsContainer">
                    <!-- Alerts will be populated by JavaScript -->
                </div>
            </section>

            <!-- Threat Analysis -->
            <section class="analysis-section">
                <div class="analysis-grid">
                    <div class="analysis-card">
                        <div class="card-header">
                            <h3>
                                <i class="fas fa-chart-pie"></i>
                                توزيع التهديدات
                            </h3>
                        </div>
                        <div class="card-content">
                            <canvas id="threatDistributionChart"></canvas>
                        </div>
                    </div>
                    <div class="analysis-card">
                        <div class="card-header">
                            <h3>
                                <i class="fas fa-globe"></i>
                                مصادر التهديدات
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="threat-sources" id="threatSources">
                                <!-- Threat sources will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- System Health -->
            <section class="health-section">
                <div class="section-header">
                    <h2>
                        <i class="fas fa-heartbeat"></i>
                        صحة النظام
                    </h2>
                </div>
                <div class="health-grid">
                    <div class="health-item">
                        <div class="health-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="health-info">
                            <h4>خادم الويب</h4>
                            <div class="health-status online">
                                <span class="status-dot"></span>
                                متصل
                            </div>
                        </div>
                        <div class="health-metrics">
                            <span>CPU: 45%</span>
                            <span>RAM: 62%</span>
                        </div>
                    </div>
                    <div class="health-item">
                        <div class="health-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="health-info">
                            <h4>قاعدة البيانات</h4>
                            <div class="health-status online">
                                <span class="status-dot"></span>
                                متصل
                            </div>
                        </div>
                        <div class="health-metrics">
                            <span>الاتصالات: 23</span>
                            <span>الاستعلامات/ث: 145</span>
                        </div>
                    </div>
                    <div class="health-item">
                        <div class="health-icon">
                            <i class="fas fa-fire-alt"></i>
                        </div>
                        <div class="health-info">
                            <h4>جدار الحماية</h4>
                            <div class="health-status online">
                                <span class="status-dot"></span>
                                نشط
                            </div>
                        </div>
                        <div class="health-metrics">
                            <span>الحظر: 12</span>
                            <span>المرور: 1,234</span>
                        </div>
                    </div>
                    <div class="health-item">
                        <div class="health-icon">
                            <i class="fas fa-shield-virus"></i>
                        </div>
                        <div class="health-info">
                            <h4>مكافح الفيروسات</h4>
                            <div class="health-status online">
                                <span class="status-dot"></span>
                                محدث
                            </div>
                        </div>
                        <div class="health-metrics">
                            <span>آخر فحص: منذ ساعة</span>
                            <span>التهديدات: 0</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 HITS Security System. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
    <script src="auth.js"></script>
    <script src="event-logger.js"></script>
    <script src="dashboard-charts.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            initializeSecurityDashboard();
            loadSecurityAlerts();
            initializeCharts();
            startRealTimeMonitoring();
        });
    </script>
</body>
</html>
