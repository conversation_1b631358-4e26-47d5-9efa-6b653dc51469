# نظام إدارة أمن المعلومات - HITS Security

نظام شامل لإدارة أمن المعلومات وتسجيل الأحداث الأمنية مع واجهة احترافية باللغة العربية.

## المميزات الرئيسية

### 🔐 الأمان والمصادقة
- مصادقة ثنائية (2FA) للمستخدمين
- تشفير البيانات الحساسة
- حماية من SQL Injection و XSS
- إدارة الجلسات الآمنة
- قفل الحسابات بعد المحاولات الفاشلة

### 📊 لوحة التحكم المتقدمة
- مراقبة الأحداث في الوقت الفعلي
- إحصائيات شاملة للأمان
- رسوم بيانية تفاعلية
- تنبيهات فورية للتهديدات
- مراقبة صحة النظام

### 📝 تسجيل الأحداث الشامل
- تسجيل جميع الأحداث الأمنية
- تصنيف الأحداث حسب الخطورة
- بحث وتصفية متقدم
- تصدير السجلات بصيغ متعددة
- أرشفة تلقائية للسجلات

### ⚙️ إعدادات قابلة للتخصيص
- إعدادات الأمان المتقدمة
- تخصيص التنبيهات
- إدارة المستخدمين
- النسخ الاحتياطي التلقائي
- تكامل مع أنظمة SIEM

### 🎨 واجهة مستخدم احترافية
- تصميم متجاوب لجميع الأجهزة
- وضع ليلي ونهاري
- ألوان زرقاء/رمادية احترافية
- دعم كامل للغة العربية
- تجربة مستخدم سلسة

## الملفات والمكونات

### الصفحات الرئيسية
- `index.html` - الصفحة الرئيسية ولوحة التحكم مع إدخال الأحداث اليدوي
- `login.html` - صفحة تسجيل الدخول
- `event-logs.html` - عرض وإدارة سجلات الأحداث
- `settings.html` - إعدادات النظام

### ملفات JavaScript
- `script.js` - الوظائف الأساسية للنظام
- `auth.js` - نظام المصادقة والأمان
- `event-logger.js` - تسجيل وإدارة الأحداث
- `settings.js` - إدارة الإعدادات

### ملفات التصميم والبيانات
- `styles.css` - ملف التنسيق الرئيسي
- `data.json` - بيانات تجريبية للنظام

## كيفية الاستخدام

### 1. تشغيل النظام
```bash
# افتح الملف index.html في المتصفح
# أو استخدم خادم ويب محلي
python -m http.server 8000
# ثم افتح http://localhost:8000
```

### 2. تسجيل الدخول
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 3. المستخدمون المتاحون
| المستخدم | كلمة المرور | الدور |
|----------|-------------|-------|
| admin | admin123 | مدير |
| security | security123 | محلل أمني |
| viewer | viewer123 | مشاهد |

## الوظائف المتقدمة

### تسجيل الأحداث
```javascript
// تسجيل حدث أمني
logSecurityEvent('LOGIN_SUCCESS', 'تسجيل دخول ناجح', 'INFO', {
    username: 'admin',
    ip: '*************'
});
```

### إدارة الإعدادات
```javascript
// الحصول على إعداد
const darkMode = getSetting('general', 'darkMode');

// تعديل إعداد
setSetting('security', 'sessionTimeout', 60);
```

### تصدير البيانات
- تصدير سجلات الأحداث (JSON, CSV, XML)
- تصدير التقارير الأمنية
- تصدير الإعدادات للنسخ الاحتياطي

## الأمان والحماية

### الحماية المدمجة
- تشفير البيانات الحساسة
- حماية CSRF
- تنظيف المدخلات (Input Sanitization)
- إدارة الجلسات الآمنة
- تحديد معدل الطلبات (Rate Limiting)

### مراقبة التهديدات
- اكتشاف النشاط المشبوه
- مراقبة محاولات الاختراق
- تنبيهات فورية للتهديدات
- حظر عناوين IP المشبوهة

## التخصيص والتطوير

### إضافة أنواع أحداث جديدة
```javascript
// في event-logger.js
EVENT_CATEGORIES.CUSTOM = {
    'CUSTOM_EVENT': 'حدث مخصص'
};
```

### تخصيص الألوان والتصميم
```css
/* في styles.css */
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
}
```

## المتطلبات التقنية

### المتصفحات المدعومة
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### المكتبات المستخدمة
- Font Awesome للأيقونات
- Google Fonts (Cairo) للخطوط العربية

## الترخيص والدعم

هذا النظام مطور خصيصاً لإدارة أمن المعلومات ويمكن تخصيصه حسب احتياجات المؤسسة.

### الميزات المستقبلية
- [ ] تكامل مع Active Directory
- [ ] تطبيق جوال
- [ ] تقارير متقدمة
- [ ] ذكاء اصطناعي لاكتشاف التهديدات
- [ ] API للتكامل مع أنظمة أخرى

## الدعم الفني

للحصول على الدعم الفني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX

---

© 2024 HITS Security System. جميع الحقوق محفوظة.
