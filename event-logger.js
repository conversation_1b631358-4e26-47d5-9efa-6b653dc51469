// Event Logger Module for Security Information Management

// Event Categories and Types
const EVENT_CATEGORIES = {
    AUTHENTICATION: {
        LOGIN_SUCCESS: 'تسجيل دخول ناجح',
        LOGIN_FAILED: 'فشل تسجيل الدخول',
        LOGIN_ERROR: 'خطأ في تسجيل الدخول',
        LOGOUT: 'تسجيل خروج',
        PASSWORD_CHANGE: 'تغيير كلمة المرور',
        TWO_FACTOR_ENABLED: 'تفعيل المصادقة الثنائية',
        TWO_FACTOR_DISABLED: 'إلغاء المصادقة الثنائية',
        ACCOUNT_LOCKED: 'قفل الحساب',
        ACCOUNT_UNLOCKED: 'إلغاء قفل الحساب'
    },
    SECURITY: {
        THREAT_DETECTED: 'تهديد مكتشف',
        MALWARE_DETECTED: 'برمجية خبيثة مكتشفة',
        SUSPICIOUS_ACTIVITY: 'نشاط مشبوه',
        SECURITY_SCAN: 'فحص أمني',
        VULNERABILITY_FOUND: 'ثغرة أمنية مكتشفة',
        FIREWALL_BLOCK: 'حظر جدار الحماية',
        INTRUSION_ATTEMPT: 'محاولة اختراق',
        DATA_BREACH_ATTEMPT: 'محاولة اختراق البيانات',
        UNAUTHORIZED_ACCESS: 'وصول غير مصرح',
        PRIVILEGE_ESCALATION: 'تصعيد الصلاحيات'
    },
    SYSTEM: {
        SYSTEM_START: 'بدء تشغيل النظام',
        SYSTEM_SHUTDOWN: 'إيقاف النظام',
        SYSTEM_ERROR: 'خطأ في النظام',
        SYSTEM_UPDATE: 'تحديث النظام',
        CONFIGURATION_CHANGE: 'تغيير في الإعدادات',
        SERVICE_START: 'بدء تشغيل الخدمة',
        SERVICE_STOP: 'إيقاف الخدمة',
        BACKUP_CREATED: 'إنشاء نسخة احتياطية',
        BACKUP_RESTORED: 'استعادة نسخة احتياطية',
        DATABASE_CONNECTION: 'اتصال قاعدة البيانات'
    },
    USER_MANAGEMENT: {
        USER_CREATED: 'إنشاء مستخدم جديد',
        USER_DELETED: 'حذف مستخدم',
        USER_MODIFIED: 'تعديل مستخدم',
        PERMISSION_CHANGE: 'تغيير الصلاحيات',
        ROLE_ASSIGNED: 'تعيين دور',
        ROLE_REMOVED: 'إزالة دور',
        GROUP_CREATED: 'إنشاء مجموعة',
        GROUP_DELETED: 'حذف مجموعة',
        GROUP_MODIFIED: 'تعديل مجموعة'
    },
    DATA_ACCESS: {
        FILE_ACCESSED: 'الوصول إلى ملف',
        FILE_MODIFIED: 'تعديل ملف',
        FILE_DELETED: 'حذف ملف',
        FILE_CREATED: 'إنشاء ملف',
        DATABASE_QUERY: 'استعلام قاعدة البيانات',
        DATA_EXPORT: 'تصدير البيانات',
        DATA_IMPORT: 'استيراد البيانات',
        SENSITIVE_DATA_ACCESS: 'الوصول إلى بيانات حساسة'
    },
    NETWORK: {
        CONNECTION_ESTABLISHED: 'إنشاء اتصال',
        CONNECTION_TERMINATED: 'إنهاء اتصال',
        NETWORK_ERROR: 'خطأ في الشبكة',
        BANDWIDTH_EXCEEDED: 'تجاوز عرض النطاق',
        PORT_SCAN_DETECTED: 'اكتشاف مسح المنافذ',
        DDoS_ATTACK: 'هجوم حجب الخدمة',
        DNS_QUERY: 'استعلام DNS',
        VPN_CONNECTION: 'اتصال VPN'
    }
};

// Severity Levels
const SEVERITY_LEVELS = {
    INFO: { level: 1, color: '#3b82f6', icon: 'fa-info-circle' },
    WARNING: { level: 2, color: '#f59e0b', icon: 'fa-exclamation-triangle' },
    ERROR: { level: 3, color: '#ef4444', icon: 'fa-times-circle' },
    CRITICAL: { level: 4, color: '#dc2626', icon: 'fa-exclamation-circle' }
};

// Event Storage Configuration
const EVENT_STORAGE = {
    maxEvents: 10000,
    storageKey: 'hits_security_events',
    indexKey: 'hits_event_index',
    compressionThreshold: 5000
};

// Main Event Logger Class
class SecurityEventLogger {
    constructor() {
        this.events = this.loadEvents();
        this.eventIndex = this.loadEventIndex();
        this.listeners = new Map();
        this.filters = new Map();
        this.initializeRealTimeLogging();
        this.initializeSampleData();
    }

    // Initialize with sample data if no events exist
    initializeSampleData() {
        if (this.events.length === 0) {
            this.loadSampleEvents();
        }
    }

    loadSampleEvents() {
        const sampleEvents = [
            {
                type: 'LOGIN_SUCCESS',
                description: 'تسجيل دخول ناجح للمستخدم admin',
                severity: 'INFO',
                metadata: { ip: '*************', userAgent: 'Mozilla/5.0' }
            },
            {
                type: 'LOGIN_FAILED',
                description: 'فشل في تسجيل الدخول - كلمة مرور خاطئة',
                severity: 'WARNING',
                metadata: { ip: '************', attemptNumber: 3 }
            },
            {
                type: 'THREAT_DETECTED',
                description: 'اكتشاف محاولة اختراق من عنوان IP مشبوه',
                severity: 'CRITICAL',
                metadata: { threatType: 'SQL_INJECTION', blocked: true }
            },
            {
                type: 'SYSTEM_UPDATE',
                description: 'تحديث قواعد الأمان بنجاح',
                severity: 'INFO',
                metadata: { updateVersion: '2024.1.15', rulesUpdated: 127 }
            },
            {
                type: 'SUSPICIOUS_ACTIVITY',
                description: 'نشاط مشبوه - محاولات وصول متعددة في وقت قصير',
                severity: 'WARNING',
                metadata: { requestCount: 150, timeWindow: '5 minutes' }
            }
        ];

        sampleEvents.forEach((eventData, index) => {
            const event = this.createEvent(
                eventData.type,
                eventData.description,
                eventData.severity,
                {
                    ...eventData.metadata,
                    simulated: true,
                    sampleData: true
                }
            );

            // Adjust timestamp to spread events over time
            const now = new Date();
            event.timestamp = new Date(now.getTime() - (index * 30 * 60 * 1000)).toISOString();

            this.events.unshift(event);
            this.updateEventIndex(event);
        });

        this.saveEvents();
        this.saveEventIndex();
    }

    // Core Logging Functions
    logEvent(type, description, severity = 'INFO', metadata = {}) {
        const event = this.createEvent(type, description, severity, metadata);
        this.storeEvent(event);
        this.notifyListeners(event);
        this.checkAlertConditions(event);
        return event;
    }

    createEvent(type, description, severity, metadata) {
        const timestamp = new Date().toISOString();
        const user = this.getCurrentUser();
        
        return {
            id: this.generateEventId(),
            type: type,
            category: this.getEventCategory(type),
            description: description,
            severity: severity,
            timestamp: timestamp,
            user: user?.username || 'system',
            userId: user?.id || null,
            userRole: user?.role || null,
            source: metadata.source || 'web_application',
            ip: metadata.ip || this.getClientIP(),
            userAgent: metadata.userAgent || navigator.userAgent,
            sessionId: this.getSessionId(),
            requestId: metadata.requestId || this.generateRequestId(),
            metadata: this.sanitizeMetadata(metadata),
            hash: this.generateEventHash(type, description, timestamp)
        };
    }

    storeEvent(event) {
        this.events.unshift(event);
        this.updateEventIndex(event);
        
        // Maintain storage limits
        if (this.events.length > EVENT_STORAGE.maxEvents) {
            const removedEvents = this.events.splice(EVENT_STORAGE.maxEvents);
            this.removeFromIndex(removedEvents);
        }
        
        this.saveEvents();
        this.saveEventIndex();
    }

    // Event Retrieval Functions
    getEvents(options = {}) {
        let filteredEvents = [...this.events];
        
        // Apply filters
        if (options.severity) {
            filteredEvents = filteredEvents.filter(event => 
                Array.isArray(options.severity) 
                    ? options.severity.includes(event.severity)
                    : event.severity === options.severity
            );
        }
        
        if (options.category) {
            filteredEvents = filteredEvents.filter(event => 
                Array.isArray(options.category)
                    ? options.category.includes(event.category)
                    : event.category === options.category
            );
        }
        
        if (options.type) {
            filteredEvents = filteredEvents.filter(event => 
                Array.isArray(options.type)
                    ? options.type.includes(event.type)
                    : event.type === options.type
            );
        }
        
        if (options.user) {
            filteredEvents = filteredEvents.filter(event => event.user === options.user);
        }
        
        if (options.startDate) {
            const startDate = new Date(options.startDate);
            filteredEvents = filteredEvents.filter(event => 
                new Date(event.timestamp) >= startDate
            );
        }
        
        if (options.endDate) {
            const endDate = new Date(options.endDate);
            filteredEvents = filteredEvents.filter(event => 
                new Date(event.timestamp) <= endDate
            );
        }
        
        if (options.search) {
            const searchTerm = options.search.toLowerCase();
            filteredEvents = filteredEvents.filter(event => 
                event.description.toLowerCase().includes(searchTerm) ||
                event.type.toLowerCase().includes(searchTerm) ||
                event.user.toLowerCase().includes(searchTerm)
            );
        }
        
        // Apply sorting
        if (options.sortBy) {
            filteredEvents.sort((a, b) => {
                const aValue = a[options.sortBy];
                const bValue = b[options.sortBy];
                
                if (options.sortOrder === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });
        }
        
        // Apply pagination
        if (options.limit) {
            const start = options.offset || 0;
            filteredEvents = filteredEvents.slice(start, start + options.limit);
        }
        
        return filteredEvents;
    }

    getRecentEvents(count = 10) {
        return this.events.slice(0, count);
    }

    getEventById(id) {
        return this.events.find(event => event.id === id);
    }

    getEventsByTimeRange(startTime, endTime) {
        const start = new Date(startTime);
        const end = new Date(endTime);
        
        return this.events.filter(event => {
            const eventTime = new Date(event.timestamp);
            return eventTime >= start && eventTime <= end;
        });
    }

    // Statistics and Analytics
    getEventStatistics(timeRange = '24h') {
        const events = this.getEventsInTimeRange(timeRange);
        
        const stats = {
            total: events.length,
            bySeverity: {},
            byCategory: {},
            byType: {},
            byUser: {},
            byHour: {},
            timeline: []
        };
        
        // Initialize counters
        Object.keys(SEVERITY_LEVELS).forEach(severity => {
            stats.bySeverity[severity] = 0;
        });
        
        Object.keys(EVENT_CATEGORIES).forEach(category => {
            stats.byCategory[category] = 0;
        });
        
        // Process events
        events.forEach(event => {
            // Severity statistics
            stats.bySeverity[event.severity]++;
            
            // Category statistics
            stats.byCategory[event.category]++;
            
            // Type statistics
            stats.byType[event.type] = (stats.byType[event.type] || 0) + 1;
            
            // User statistics
            stats.byUser[event.user] = (stats.byUser[event.user] || 0) + 1;
            
            // Hourly statistics
            const hour = new Date(event.timestamp).getHours();
            stats.byHour[hour] = (stats.byHour[hour] || 0) + 1;
        });
        
        // Generate timeline
        stats.timeline = this.generateTimeline(events, timeRange);
        
        return stats;
    }

    generateTimeline(events, timeRange) {
        const timeline = [];
        const now = new Date();
        let interval, format;
        
        switch (timeRange) {
            case '1h':
                interval = 5 * 60 * 1000; // 5 minutes
                format = 'HH:mm';
                break;
            case '24h':
                interval = 60 * 60 * 1000; // 1 hour
                format = 'HH:00';
                break;
            case '7d':
                interval = 24 * 60 * 60 * 1000; // 1 day
                format = 'MM/DD';
                break;
            case '30d':
                interval = 24 * 60 * 60 * 1000; // 1 day
                format = 'MM/DD';
                break;
            default:
                interval = 60 * 60 * 1000; // 1 hour
                format = 'HH:00';
        }
        
        const periods = this.getTimePeriods(timeRange);
        
        periods.forEach(period => {
            const periodEvents = events.filter(event => {
                const eventTime = new Date(event.timestamp);
                return eventTime >= period.start && eventTime < period.end;
            });
            
            timeline.push({
                time: period.start.toLocaleTimeString('ar-SA', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                }),
                count: periodEvents.length,
                critical: periodEvents.filter(e => e.severity === 'CRITICAL').length,
                error: periodEvents.filter(e => e.severity === 'ERROR').length,
                warning: periodEvents.filter(e => e.severity === 'WARNING').length,
                info: periodEvents.filter(e => e.severity === 'INFO').length
            });
        });
        
        return timeline;
    }

    // Alert and Notification System
    checkAlertConditions(event) {
        const alerts = [];
        
        // Critical event alert
        if (event.severity === 'CRITICAL') {
            alerts.push({
                type: 'CRITICAL_EVENT',
                message: `حدث حرج: ${event.description}`,
                event: event
            });
        }
        
        // Multiple failed logins
        if (event.type === 'LOGIN_FAILED') {
            const recentFailures = this.getEvents({
                type: 'LOGIN_FAILED',
                user: event.user,
                startDate: new Date(Date.now() - 15 * 60 * 1000) // Last 15 minutes
            });
            
            if (recentFailures.length >= 3) {
                alerts.push({
                    type: 'MULTIPLE_FAILED_LOGINS',
                    message: `محاولات دخول متعددة فاشلة للمستخدم: ${event.user}`,
                    event: event,
                    count: recentFailures.length
                });
            }
        }
        
        // Suspicious activity patterns
        if (this.detectSuspiciousActivity(event)) {
            alerts.push({
                type: 'SUSPICIOUS_ACTIVITY',
                message: `نشاط مشبوه مكتشف من المستخدم: ${event.user}`,
                event: event
            });
        }
        
        // Process alerts
        alerts.forEach(alert => this.processAlert(alert));
    }

    detectSuspiciousActivity(event) {
        // Check for rapid successive events from same user
        const recentEvents = this.getEvents({
            user: event.user,
            startDate: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
        });
        
        if (recentEvents.length > 20) {
            return true;
        }
        
        // Check for unusual access patterns
        if (event.category === 'DATA_ACCESS') {
            const userHistory = this.getEvents({
                user: event.user,
                category: 'DATA_ACCESS',
                startDate: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            });
            
            const avgAccess = userHistory.length / 24;
            const recentAccess = this.getEvents({
                user: event.user,
                category: 'DATA_ACCESS',
                startDate: new Date(Date.now() - 60 * 60 * 1000) // Last hour
            }).length;
            
            if (recentAccess > avgAccess * 3) {
                return true;
            }
        }
        
        return false;
    }

    processAlert(alert) {
        // Log the alert as an event
        this.logEvent('SECURITY_ALERT', alert.message, 'WARNING', {
            alertType: alert.type,
            originalEvent: alert.event.id,
            metadata: alert
        });
        
        // Show notification to user
        if (typeof showNotification === 'function') {
            showNotification(alert.message, 'warning', 5000);
        }
        
        // In production, send to SIEM or notification system
        this.sendToSIEM(alert);
    }

    // Export and Import Functions
    exportEvents(format = 'json', options = {}) {
        const events = this.getEvents(options);
        
        switch (format.toLowerCase()) {
            case 'json':
                return this.exportAsJSON(events);
            case 'csv':
                return this.exportAsCSV(events);
            case 'xml':
                return this.exportAsXML(events);
            default:
                throw new Error('Unsupported export format');
        }
    }

    exportAsJSON(events) {
        return JSON.stringify({
            exportDate: new Date().toISOString(),
            totalEvents: events.length,
            events: events
        }, null, 2);
    }

    exportAsCSV(events) {
        const headers = [
            'ID', 'Timestamp', 'Type', 'Category', 'Severity', 
            'User', 'Description', 'Source', 'IP'
        ];
        
        const csvContent = [
            headers.join(','),
            ...events.map(event => [
                event.id,
                event.timestamp,
                event.type,
                event.category,
                event.severity,
                event.user,
                `"${event.description.replace(/"/g, '""')}"`,
                event.source,
                event.ip
            ].join(','))
        ].join('\n');
        
        return csvContent;
    }

    exportAsXML(events) {
        let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
        xml += '<SecurityEvents>\n';
        xml += `  <ExportDate>${new Date().toISOString()}</ExportDate>\n`;
        xml += `  <TotalEvents>${events.length}</TotalEvents>\n`;
        xml += '  <Events>\n';
        
        events.forEach(event => {
            xml += '    <Event>\n';
            Object.entries(event).forEach(([key, value]) => {
                if (typeof value === 'object') {
                    xml += `      <${key}><![CDATA[${JSON.stringify(value)}]]></${key}>\n`;
                } else {
                    xml += `      <${key}><![CDATA[${value}]]></${key}>\n`;
                }
            });
            xml += '    </Event>\n';
        });
        
        xml += '  </Events>\n';
        xml += '</SecurityEvents>';
        
        return xml;
    }

    // Utility Functions
    generateEventId() {
        return 'evt_' + Date.now().toString(36) + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateRequestId() {
        return 'req_' + Date.now().toString(36) + '_' + Math.random().toString(36).substr(2, 5);
    }

    generateEventHash(type, description, timestamp) {
        const data = type + description + timestamp;
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString(36);
    }

    getEventCategory(type) {
        for (const [category, types] of Object.entries(EVENT_CATEGORIES)) {
            if (types[type]) {
                return category;
            }
        }
        return 'UNKNOWN';
    }

    getCurrentUser() {
        try {
            const userData = localStorage.getItem('hits_security_user');
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            return null;
        }
    }

    getSessionId() {
        return sessionStorage.getItem('sessionId') || 'unknown';
    }

    getClientIP() {
        // In production, this would be provided by the server
        return 'localhost';
    }

    sanitizeMetadata(metadata) {
        const sanitized = {};
        for (const [key, value] of Object.entries(metadata)) {
            if (typeof value === 'string') {
                sanitized[key] = value.substring(0, 1000); // Limit string length
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = JSON.stringify(value).substring(0, 2000);
            } else {
                sanitized[key] = value;
            }
        }
        return sanitized;
    }

    // Storage Management
    loadEvents() {
        try {
            const eventsData = localStorage.getItem(EVENT_STORAGE.storageKey);
            return eventsData ? JSON.parse(eventsData) : [];
        } catch (error) {
            console.error('Error loading events:', error);
            return [];
        }
    }

    saveEvents() {
        try {
            localStorage.setItem(EVENT_STORAGE.storageKey, JSON.stringify(this.events));
        } catch (error) {
            console.error('Error saving events:', error);
            // If storage is full, remove oldest events and try again
            if (error.name === 'QuotaExceededError') {
                this.events = this.events.slice(0, Math.floor(this.events.length * 0.8));
                this.saveEvents();
            }
        }
    }

    loadEventIndex() {
        try {
            const indexData = localStorage.getItem(EVENT_STORAGE.indexKey);
            return indexData ? JSON.parse(indexData) : {};
        } catch (error) {
            console.error('Error loading event index:', error);
            return {};
        }
    }

    saveEventIndex() {
        try {
            localStorage.setItem(EVENT_STORAGE.indexKey, JSON.stringify(this.eventIndex));
        } catch (error) {
            console.error('Error saving event index:', error);
        }
    }

    updateEventIndex(event) {
        // Index by type
        if (!this.eventIndex.byType) this.eventIndex.byType = {};
        if (!this.eventIndex.byType[event.type]) this.eventIndex.byType[event.type] = [];
        this.eventIndex.byType[event.type].unshift(event.id);
        
        // Index by user
        if (!this.eventIndex.byUser) this.eventIndex.byUser = {};
        if (!this.eventIndex.byUser[event.user]) this.eventIndex.byUser[event.user] = [];
        this.eventIndex.byUser[event.user].unshift(event.id);
        
        // Index by severity
        if (!this.eventIndex.bySeverity) this.eventIndex.bySeverity = {};
        if (!this.eventIndex.bySeverity[event.severity]) this.eventIndex.bySeverity[event.severity] = [];
        this.eventIndex.bySeverity[event.severity].unshift(event.id);
    }

    // Real-time Logging
    initializeRealTimeLogging() {
        // Monitor console errors
        const originalError = console.error;
        console.error = (...args) => {
            this.logEvent('SYSTEM_ERROR', args.join(' '), 'ERROR', {
                source: 'console',
                stack: new Error().stack
            });
            originalError.apply(console, args);
        };
        
        // Monitor unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logEvent('SYSTEM_ERROR', `Unhandled promise rejection: ${event.reason}`, 'ERROR', {
                source: 'promise',
                reason: event.reason
            });
        });
        
        // Monitor page visibility changes
        document.addEventListener('visibilitychange', () => {
            this.logEvent('USER_ACTIVITY', 
                document.hidden ? 'Page hidden' : 'Page visible', 
                'INFO', {
                    visibility: document.hidden ? 'hidden' : 'visible'
                }
            );
        });
    }

    // SIEM Integration
    sendToSIEM(data) {
        // In production, this would send data to a SIEM system
        console.log('SIEM Data:', data);
        
        // Example: Send to external SIEM via API
        /*
        fetch('/api/siem/events', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + getAuthToken()
            },
            body: JSON.stringify(data)
        }).catch(error => {
            console.error('Failed to send to SIEM:', error);
        });
        */
    }

    // Helper methods for time ranges
    getEventsInTimeRange(timeRange) {
        const now = new Date();
        let startTime;
        
        switch (timeRange) {
            case '1h':
                startTime = new Date(now.getTime() - 60 * 60 * 1000);
                break;
            case '24h':
                startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            default:
                startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        }
        
        return this.getEventsByTimeRange(startTime, now);
    }

    getTimePeriods(timeRange) {
        const now = new Date();
        const periods = [];
        let interval, count;
        
        switch (timeRange) {
            case '1h':
                interval = 5 * 60 * 1000; // 5 minutes
                count = 12;
                break;
            case '24h':
                interval = 60 * 60 * 1000; // 1 hour
                count = 24;
                break;
            case '7d':
                interval = 24 * 60 * 60 * 1000; // 1 day
                count = 7;
                break;
            case '30d':
                interval = 24 * 60 * 60 * 1000; // 1 day
                count = 30;
                break;
            default:
                interval = 60 * 60 * 1000;
                count = 24;
        }
        
        for (let i = count - 1; i >= 0; i--) {
            const start = new Date(now.getTime() - (i + 1) * interval);
            const end = new Date(now.getTime() - i * interval);
            periods.push({ start, end });
        }
        
        return periods;
    }
}

// Create global instance
const eventLogger = new SecurityEventLogger();

// Global functions for easy access
function logSecurityEvent(type, description, severity = 'INFO', metadata = {}) {
    return eventLogger.logEvent(type, description, severity, metadata);
}

function getSecurityEvents(options = {}) {
    return eventLogger.getEvents(options);
}

function getRecentSecurityEvents(count = 10) {
    return eventLogger.getRecentEvents(count);
}

function getSecurityEventStatistics(timeRange = '24h') {
    return eventLogger.getEventStatistics(timeRange);
}

function exportSecurityEvents(format = 'json', options = {}) {
    return eventLogger.exportEvents(format, options);
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SecurityEventLogger,
        EVENT_CATEGORIES,
        SEVERITY_LEVELS,
        logSecurityEvent,
        getSecurityEvents,
        getRecentSecurityEvents,
        getSecurityEventStatistics,
        exportSecurityEvents
    };
}

// Make available globally
window.HITSEventLogger = {
    eventLogger,
    logSecurityEvent,
    getSecurityEvents,
    getRecentSecurityEvents,
    getSecurityEventStatistics,
    exportSecurityEvents,
    EVENT_CATEGORIES,
    SEVERITY_LEVELS
};
