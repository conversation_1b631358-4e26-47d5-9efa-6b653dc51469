// Authentication and Security Module

// Mock user database (in production, this would be server-side)
const MOCK_USERS = {
    'admin': {
        password: 'admin123', // In production, this would be hashed
        twoFactorSecret: '123456', // Mock 2FA secret
        role: 'administrator',
        permissions: ['read', 'write', 'delete', 'admin'],
        lastLogin: null,
        loginAttempts: 0,
        locked: false
    },
    'security': {
        password: 'security123',
        twoFactorSecret: '654321',
        role: 'security_analyst',
        permissions: ['read', 'write'],
        lastLogin: null,
        loginAttempts: 0,
        locked: false
    },
    'viewer': {
        password: 'viewer123',
        twoFactorSecret: '789012',
        role: 'viewer',
        permissions: ['read'],
        lastLogin: null,
        loginAttempts: 0,
        locked: false
    }
};

// Security Configuration
const SECURITY_CONFIG = {
    maxLoginAttempts: 3,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    passwordMinLength: 8,
    requireTwoFactor: false, // تم إلغاء التحقق الثنائي
    sessionKey: 'hits_security_session',
    userKey: 'hits_security_user'
};

// Authentication Functions
function authenticateUser(username, password) {
    return new Promise((resolve) => {
        // Simulate network delay
        setTimeout(() => {
            const user = MOCK_USERS[username];

            // Check if user exists
            if (!user) {
                resolve({
                    success: false,
                    error: 'اسم المستخدم غير موجود'
                });
                return;
            }

            // Check if account is locked
            if (user.locked) {
                resolve({
                    success: false,
                    error: 'الحساب مقفل مؤقتاً. يرجى المحاولة لاحقاً'
                });
                return;
            }

            // Check password
            if (user.password !== password) {
                user.loginAttempts++;

                if (user.loginAttempts >= SECURITY_CONFIG.maxLoginAttempts) {
                    user.locked = true;
                    setTimeout(() => {
                        user.locked = false;
                        user.loginAttempts = 0;
                    }, SECURITY_CONFIG.lockoutDuration);

                    resolve({
                        success: false,
                        error: 'تم قفل الحساب بسبب محاولات الدخول المتكررة'
                    });
                } else {
                    const remainingAttempts = SECURITY_CONFIG.maxLoginAttempts - user.loginAttempts;
                    resolve({
                        success: false,
                        error: `كلمة المرور غير صحيحة. المحاولات المتبقية: ${remainingAttempts}`
                    });
                }
                return;
            }

            // Successful authentication
            user.loginAttempts = 0;
            user.lastLogin = new Date().toISOString();

            const sessionData = {
                username: username,
                role: user.role,
                permissions: user.permissions,
                loginTime: Date.now(),
                lastActivity: Date.now()
            };

            // Store session data
            localStorage.setItem(SECURITY_CONFIG.sessionKey, JSON.stringify(sessionData));
            localStorage.setItem(SECURITY_CONFIG.userKey, JSON.stringify({
                username: username,
                role: user.role,
                permissions: user.permissions
            }));
            localStorage.setItem('loginTime', Date.now().toString());

            resolve({
                success: true,
                user: {
                    username: username,
                    role: user.role,
                    permissions: user.permissions
                }
            });
        }, 1000);
    });
}

function validateTwoFactorCode(secret, code) {
    // Mock 2FA validation - in production, use proper TOTP validation
    const validCodes = [secret, '000000', '123456']; // Mock valid codes
    return validCodes.includes(code);
}

function isAuthenticated() {
    const sessionData = getSessionData();
    if (!sessionData) return false;
    
    // Check session timeout
    const now = Date.now();
    const timeSinceLastActivity = now - sessionData.lastActivity;
    
    if (timeSinceLastActivity > SECURITY_CONFIG.sessionTimeout) {
        clearAuthData();
        return false;
    }
    
    // Update last activity
    sessionData.lastActivity = now;
    localStorage.setItem(SECURITY_CONFIG.sessionKey, JSON.stringify(sessionData));
    
    return true;
}

function getSessionData() {
    try {
        const sessionData = localStorage.getItem(SECURITY_CONFIG.sessionKey);
        return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
        console.error('Error parsing session data:', error);
        return null;
    }
}

function getCurrentUser() {
    try {
        const userData = localStorage.getItem(SECURITY_CONFIG.userKey);
        return userData ? JSON.parse(userData) : null;
    } catch (error) {
        console.error('Error parsing user data:', error);
        return null;
    }
}

function hasPermission(permission) {
    const user = getCurrentUser();
    return user && user.permissions && user.permissions.includes(permission);
}

function clearAuthData() {
    localStorage.removeItem(SECURITY_CONFIG.sessionKey);
    localStorage.removeItem(SECURITY_CONFIG.userKey);
    localStorage.removeItem('loginTime');
    localStorage.removeItem('rememberUser');
}

// Password Security Functions
function validatePassword(password) {
    const errors = [];
    
    if (password.length < SECURITY_CONFIG.passwordMinLength) {
        errors.push(`كلمة المرور يجب أن تكون ${SECURITY_CONFIG.passwordMinLength} أحرف على الأقل`);
    }
    
    if (!/[A-Z]/.test(password)) {
        errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
    }
    
    if (!/[a-z]/.test(password)) {
        errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
    }
    
    if (!/[0-9]/.test(password)) {
        errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
    }
    
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
        errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

function generateSecurePassword(length = 12) {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    let password = '';
    
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return password;
}

// Security Monitoring
function checkSecurityThreats() {
    const threats = [];
    
    // Check for suspicious login patterns
    const failedLogins = getFailedLoginAttempts();
    if (failedLogins.length > 5) {
        threats.push({
            type: 'SUSPICIOUS_LOGIN_ACTIVITY',
            severity: 'WARNING',
            description: 'Multiple failed login attempts detected',
            count: failedLogins.length
        });
    }
    
    // Check session security
    const sessionData = getSessionData();
    if (sessionData) {
        const sessionAge = Date.now() - sessionData.loginTime;
        if (sessionAge > 24 * 60 * 60 * 1000) { // 24 hours
            threats.push({
                type: 'LONG_SESSION',
                severity: 'INFO',
                description: 'User session has been active for more than 24 hours',
                duration: sessionAge
            });
        }
    }
    
    // Check for multiple tabs/windows
    if (typeof(Storage) !== "undefined") {
        const tabId = sessionStorage.getItem('tabId') || Math.random().toString(36).substr(2, 9);
        sessionStorage.setItem('tabId', tabId);
        
        const activeTabs = JSON.parse(localStorage.getItem('activeTabs') || '[]');
        if (!activeTabs.includes(tabId)) {
            activeTabs.push(tabId);
            localStorage.setItem('activeTabs', JSON.stringify(activeTabs));
        }
        
        if (activeTabs.length > 3) {
            threats.push({
                type: 'MULTIPLE_SESSIONS',
                severity: 'WARNING',
                description: 'Multiple browser tabs/windows detected',
                count: activeTabs.length
            });
        }
    }
    
    return threats;
}

function getFailedLoginAttempts() {
    // In a real application, this would query server logs
    const attempts = JSON.parse(localStorage.getItem('failedLoginAttempts') || '[]');
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    return attempts.filter(attempt => attempt.timestamp > oneHourAgo);
}

function logFailedLoginAttempt(username, reason) {
    const attempts = JSON.parse(localStorage.getItem('failedLoginAttempts') || '[]');
    attempts.push({
        username: username,
        reason: reason,
        timestamp: Date.now(),
        ip: 'localhost', // In production, get real IP
        userAgent: navigator.userAgent
    });
    
    // Keep only last 100 attempts
    if (attempts.length > 100) {
        attempts.splice(0, attempts.length - 100);
    }
    
    localStorage.setItem('failedLoginAttempts', JSON.stringify(attempts));
}

// Input Sanitization (XSS Protection)
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    return input
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');
}

function validateInput(input, type = 'text') {
    if (!input || typeof input !== 'string') return false;
    
    switch (type) {
        case 'username':
            return /^[a-zA-Z0-9_]{3,20}$/.test(input);
        case 'email':
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
        case 'password':
            return input.length >= SECURITY_CONFIG.passwordMinLength;
        case 'alphanumeric':
            return /^[a-zA-Z0-9\s]+$/.test(input);
        case 'numeric':
            return /^[0-9]+$/.test(input);
        default:
            return input.length > 0;
    }
}

// CSRF Protection
function generateCSRFToken() {
    return Math.random().toString(36).substr(2) + Date.now().toString(36);
}

function getCSRFToken() {
    let token = sessionStorage.getItem('csrfToken');
    if (!token) {
        token = generateCSRFToken();
        sessionStorage.setItem('csrfToken', token);
    }
    return token;
}

function validateCSRFToken(token) {
    const storedToken = sessionStorage.getItem('csrfToken');
    return token === storedToken;
}

// Rate Limiting
const rateLimiter = {
    requests: new Map(),
    
    isAllowed(key, maxRequests = 10, windowMs = 60000) {
        const now = Date.now();
        const windowStart = now - windowMs;
        
        if (!this.requests.has(key)) {
            this.requests.set(key, []);
        }
        
        const requests = this.requests.get(key);
        
        // Remove old requests outside the window
        const validRequests = requests.filter(timestamp => timestamp > windowStart);
        this.requests.set(key, validRequests);
        
        if (validRequests.length >= maxRequests) {
            return false;
        }
        
        validRequests.push(now);
        return true;
    }
};

// Security Event Logging
function logSecurityEvent(type, description, severity, metadata = {}) {
    const event = {
        id: generateEventId(),
        type: type,
        description: description,
        severity: severity,
        timestamp: new Date().toISOString(),
        user: getCurrentUser()?.username || 'anonymous',
        ip: 'localhost', // In production, get real IP
        userAgent: navigator.userAgent,
        metadata: metadata
    };
    
    // Store event
    const events = JSON.parse(localStorage.getItem('securityEvents') || '[]');
    events.unshift(event);
    
    // Keep only last 1000 events
    if (events.length > 1000) {
        events.splice(1000);
    }
    
    localStorage.setItem('securityEvents', JSON.stringify(events));
    
    // Log to console for debugging
    console.log('Security Event:', event);
    
    // In production, send to SIEM system
    // sendToSIEM(event);
    
    return event;
}

function generateEventId() {
    return 'evt_' + Date.now().toString(36) + '_' + Math.random().toString(36).substr(2, 5);
}

// Session Management
function extendSession() {
    const sessionData = getSessionData();
    if (sessionData) {
        sessionData.lastActivity = Date.now();
        localStorage.setItem(SECURITY_CONFIG.sessionKey, JSON.stringify(sessionData));
    }
}

function getSessionTimeRemaining() {
    const sessionData = getSessionData();
    if (!sessionData) return 0;
    
    const timeElapsed = Date.now() - sessionData.lastActivity;
    const timeRemaining = SECURITY_CONFIG.sessionTimeout - timeElapsed;
    
    return Math.max(0, timeRemaining);
}

// Initialize security monitoring
document.addEventListener('DOMContentLoaded', function() {
    // Check for security threats periodically
    setInterval(() => {
        const threats = checkSecurityThreats();
        threats.forEach(threat => {
            logSecurityEvent(threat.type, threat.description, threat.severity, threat);
        });
    }, 60000); // Check every minute
    
    // Extend session on user activity
    ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
        document.addEventListener(event, extendSession, { passive: true });
    });
    
    // Clean up tab tracking on page unload
    window.addEventListener('beforeunload', function() {
        const tabId = sessionStorage.getItem('tabId');
        if (tabId) {
            const activeTabs = JSON.parse(localStorage.getItem('activeTabs') || '[]');
            const updatedTabs = activeTabs.filter(id => id !== tabId);
            localStorage.setItem('activeTabs', JSON.stringify(updatedTabs));
        }
    });
});

// Export functions for global use
window.HITSAuth = {
    authenticateUser,
    isAuthenticated,
    getCurrentUser,
    hasPermission,
    clearAuthData,
    validatePassword,
    generateSecurePassword,
    sanitizeInput,
    validateInput,
    getCSRFToken,
    validateCSRFToken,
    rateLimiter,
    logSecurityEvent,
    extendSession,
    getSessionTimeRemaining
};
